# 📧 Guide de Configuration Email

## 🚀 Configuration Rapide

### Option 1: Script Python (Recommandé)
```bash
python3 setup_email.py
```

### Option 2: Interface Web
1. Allez sur http://127.0.0.1:5002/config
2. Remplissez les champs email
3. Testez la configuration

## 📋 Paramètres pour Gmail

### Configuration Gmail
- **Serveur SMTP**: `smtp.gmail.com`
- **Port**: `587`
- **Utilisateur**: `<EMAIL>`
- **Mot de passe**: `mot-de-passe-application` (PAS votre mot de passe normal)
- **Expéditeur**: `<EMAIL>`

### 🔐 Créer un Mot de Passe d'Application Gmail

1. **Activer la 2FA** sur votre compte Google (obligatoire)
2. Allez sur https://myaccount.google.com/security
3. Cliquez sur **"Mots de passe d'application"**
4. <PERSON><PERSON><PERSON>ionnez **"Autre (nom personnalisé)"**
5. <PERSON><PERSON>z **"Monitoring System"**
6. Copiez le mot de passe généré (16 caractères)
7. Utilisez ce mot de passe dans la configuration

## 📋 Paramètres pour Autres Fournisseurs

### Outlook/Hotmail
- **Serveur SMTP**: `smtp-mail.outlook.com`
- **Port**: `587`
- **Utilisateur**: `<EMAIL>`
- **Mot de passe**: `votre-mot-de-passe`

### Yahoo Mail
- **Serveur SMTP**: `smtp.mail.yahoo.com`
- **Port**: `587`
- **Utilisateur**: `<EMAIL>`
- **Mot de passe**: `mot-de-passe-application`

### Serveur SMTP Personnalisé
- **Serveur SMTP**: `mail.votre-domaine.com`
- **Port**: `587` ou `465` (SSL)
- **Utilisateur**: `<EMAIL>`
- **Mot de passe**: `votre-mot-de-passe`

## 🔧 Dépannage

### Problème: "Configuration email incomplète"
- Vérifiez que tous les champs sont remplis
- Vérifiez qu'il n'y a pas d'espaces en début/fin
- Utilisez le script `setup_email.py` pour une configuration guidée

### Problème: "Erreur d'authentification"
- **Gmail**: Utilisez un mot de passe d'application, pas votre mot de passe normal
- **Outlook**: Activez l'authentification moins sécurisée ou utilisez OAuth
- **Yahoo**: Utilisez un mot de passe d'application

### Problème: "Connexion refusée"
- Vérifiez le serveur SMTP et le port
- Vérifiez votre connexion internet
- Certains réseaux d'entreprise bloquent SMTP

### Problème: "Certificat SSL"
- Utilisez le port 587 avec STARTTLS
- Évitez le port 465 sauf si nécessaire

## 🧪 Test de Configuration

### Via Interface Web
1. Allez sur http://127.0.0.1:5002/config
2. Cliquez sur **"Tester la configuration"**
3. Vérifiez votre boîte email

### Via Script Python
```bash
python3 setup_email.py
# Choisir option 2 ou 3
```

### Via Logs
Surveillez les logs de l'application pour voir les messages d'erreur détaillés.

## 📝 Exemple de Configuration Complète

```python
# Configuration Gmail
smtp_server = "smtp.gmail.com"
smtp_port = 587
smtp_user = "<EMAIL>"
smtp_password = "abcd efgh ijkl mnop"  # Mot de passe d'application
email_from = "<EMAIL>"
```

## 🔔 Types d'Alertes Envoyées

Le système enverra des emails pour :
- **Machines hors ligne** (sévérité: HIGH)
- **Machines de nouveau en ligne** (sévérité: INFO)
- **Règles d'alerte personnalisées** (sévérité configurable)
- **CPU/Mémoire/Disque élevés** (selon vos règles)

## 🛡️ Sécurité

- **Ne jamais** utiliser votre mot de passe principal
- Utilisez des **mots de passe d'application** quand possible
- Stockez les mots de passe de façon sécurisée
- Limitez les permissions du compte email utilisé

## 📞 Support

Si vous avez des problèmes :
1. Vérifiez les logs de l'application
2. Testez avec un client email standard (Thunderbird, etc.)
3. Vérifiez les paramètres de sécurité de votre fournisseur email
4. Consultez la documentation de votre fournisseur SMTP
