#!/usr/bin/env python3
"""
Script de démarrage pour le serveur de monitoring
Permet de choisir le mode d'accès (local ou réseau)
"""

import sys
import socket
import subprocess
from app import app

def get_local_ip():
    """Obtient l'adresse IP locale de la machine"""
    try:
        # Connexion temporaire pour obtenir l'IP locale
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        return local_ip
    except:
        return "IP non détectée"

def check_port_available(host, port):
    """Vérifie si le port est disponible"""
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        s.settimeout(1)
        result = s.connect_ex((host, port))
        s.close()
        return result != 0
    except:
        return False

def start_server(mode='network', port=5002):
    """Démarre le serveur selon le mode choisi"""
    
    if mode == 'local':
        host = '127.0.0.1'
        access_info = f"Accès LOCAL uniquement: http://127.0.0.1:{port}"
    else:
        host = '0.0.0.0'
        local_ip = get_local_ip()
        access_info = f"""Accès RÉSEAU activé:
  - Local: http://127.0.0.1:{port}
  - Réseau: http://{local_ip}:{port}
  - Autres machines peuvent accéder via: http://{local_ip}:{port}"""
    
    # Vérifier si le port est disponible
    if not check_port_available('127.0.0.1', port):
        print(f"❌ Erreur: Le port {port} est déjà utilisé!")
        print("Essayez un autre port ou arrêtez l'autre application.")
        return False
    
    print("🚀 Démarrage du serveur de monitoring...")
    print("=" * 60)
    print(f"Mode: {'LOCAL' if mode == 'local' else 'RÉSEAU'}")
    print(f"Port: {port}")
    print(access_info)
    print("=" * 60)
    print("Appuyez sur Ctrl+C pour arrêter le serveur")
    print()
    
    try:
        app.run(debug=True, host=host, port=port)
    except KeyboardInterrupt:
        print("\n🛑 Serveur arrêté par l'utilisateur")
    except Exception as e:
        print(f"❌ Erreur lors du démarrage: {e}")

def main():
    """Menu principal"""
    print("🖥️  Serveur de Monitoring Réseau")
    print("=" * 40)
    print()
    
    if len(sys.argv) > 1:
        # Mode ligne de commande
        mode = sys.argv[1].lower()
        port = int(sys.argv[2]) if len(sys.argv) > 2 else 5002
        
        if mode in ['local', 'l']:
            start_server('local', port)
        elif mode in ['network', 'net', 'n']:
            start_server('network', port)
        else:
            print("❌ Mode invalide. Utilisez: local, network")
            sys.exit(1)
    else:
        # Mode interactif
        print("Comment voulez-vous démarrer le serveur ?")
        print()
        print("1. 🏠 LOCAL uniquement (127.0.0.1) - Plus sécurisé")
        print("2. 🌐 RÉSEAU (0.0.0.0) - Accessible depuis d'autres machines")
        print("3. ❌ Annuler")
        print()
        
        choice = input("Votre choix (1/2/3): ").strip()
        
        if choice == "1":
            port = input(f"Port (défaut: 5002): ").strip()
            port = int(port) if port.isdigit() else 5002
            start_server('local', port)
        elif choice == "2":
            port = input(f"Port (défaut: 5002): ").strip()
            port = int(port) if port.isdigit() else 5002
            
            print()
            print("⚠️  ATTENTION - Mode Réseau")
            print("- Votre application sera accessible depuis d'autres machines")
            print("- Assurez-vous que votre réseau est sécurisé")
            print("- Considérez l'ajout d'un pare-feu si nécessaire")
            print()
            
            confirm = input("Continuer ? (o/N): ").strip().lower()
            if confirm in ['o', 'oui', 'y', 'yes']:
                start_server('network', port)
            else:
                print("❌ Démarrage annulé")
        elif choice == "3":
            print("❌ Démarrage annulé")
        else:
            print("❌ Choix invalide")

if __name__ == "__main__":
    main()
