"""
Configuration pour l'application de monitoring
"""
import os

class Config:
    # Configuration de base
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'dev-secret-key-change-in-production'
    
    # Base de données
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///instance/monitoring.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # Configuration réseau
    HOST = os.environ.get('FLASK_HOST') or '0.0.0.0'  # Accessible depuis le réseau
    PORT = int(os.environ.get('FLASK_PORT') or 5002)
    DEBUG = os.environ.get('FLASK_DEBUG') or True
    
    # Sécurité
    ALLOWED_HOSTS = os.environ.get('ALLOWED_HOSTS', '').split(',') if os.environ.get('ALLOWED_HOSTS') else []

class DevelopmentConfig(Config):
    """Configuration pour développement - accès réseau autorisé"""
    DEBUG = True
    HOST = '0.0.0.0'  # Accessible depuis le réseau local

class ProductionConfig(Config):
    """Configuration pour production - plus sécurisée"""
    DEBUG = False
    HOST = '0.0.0.0'
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'change-this-in-production'

class LocalConfig(Config):
    """Configuration locale uniquement"""
    DEBUG = True
    HOST = '127.0.0.1'  # Accessible uniquement en local

# Configuration par défaut
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'local': LocalConfig,
    'default': DevelopmentConfig
}
