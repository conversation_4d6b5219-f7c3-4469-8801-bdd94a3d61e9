# 🌐 Guide d'Accès Réseau - Monitoring System

## 🚀 Démarrage Rapide

### Option 1: Script Interactif (Recommandé)
```bash
python3 run_server.py
```
Choisissez le mode d'accès dans le menu.

### Option 2: Ligne de Commande
```bash
# Accès réseau (accessible depuis d'autres machines)
python3 run_server.py network

# Accès local uniquement
python3 run_server.py local

# Avec port personnalisé
python3 run_server.py network 8080
```

### Option 3: Modification Directe
Le fichier `app.py` a été modifié pour accepter les connexions réseau.

## 🔗 URLs d'Accès

### Après Démarrage en Mode Réseau
- **Votre machine**: http://127.0.0.1:5002
- **Autres machines**: http://VOTRE-IP-LOCALE:5002

### Comment Trouver Votre IP Locale
```bash
# Linux/Mac
ip addr show | grep inet
# ou
ifconfig | grep inet

# Windows
ipconfig
```

Exemple d'IP locale: `*************`, `***********`, `*********`

## 🛡️ Considérations de Sécurité

### ⚠️ Risques du Mode Réseau
- **Accès non restreint**: Toute personne sur le réseau peut accéder
- **Données sensibles**: Informations sur votre infrastructure visibles
- **Pas d'authentification**: Aucun système de login par défaut

### 🔒 Recommandations de Sécurité

#### 1. Réseau de Confiance Uniquement
- Utilisez uniquement sur des réseaux privés/sécurisés
- Évitez les réseaux publics (café, hôtel, etc.)

#### 2. Pare-feu
```bash
# Ubuntu/Debian - Autoriser seulement le réseau local
sudo ufw allow from ***********/16 to any port 5002
sudo ufw allow from **********/12 to any port 5002
sudo ufw allow from 10.0.0.0/8 to any port 5002

# Bloquer tout le reste
sudo ufw deny 5002
```

#### 3. Proxy Reverse avec Authentification
```nginx
# Exemple Nginx avec authentification basique
server {
    listen 80;
    server_name monitoring.votre-domaine.com;
    
    auth_basic "Monitoring Access";
    auth_basic_user_file /etc/nginx/.htpasswd;
    
    location / {
        proxy_pass http://127.0.0.1:5002;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

## 🖥️ Accès depuis Différents Appareils

### Ordinateurs sur le Réseau
1. Ouvrez un navigateur
2. Tapez: `http://IP-DU-SERVEUR:5002`
3. Exemple: `http://*************:5002`

### Smartphones/Tablettes
1. Connectez-vous au même WiFi
2. Ouvrez le navigateur mobile
3. Tapez la même URL: `http://IP-DU-SERVEUR:5002`

### Autres Serveurs
```bash
# Test de connectivité
curl http://IP-DU-SERVEUR:5002

# Surveillance automatisée
wget -q --spider http://IP-DU-SERVEUR:5002 && echo "Monitoring accessible"
```

## 🔧 Dépannage

### Problème: "Connexion refusée"
1. **Vérifiez le mode de démarrage**: Utilisez `0.0.0.0` pas `127.0.0.1`
2. **Pare-feu**: Vérifiez que le port n'est pas bloqué
3. **IP correcte**: Vérifiez l'IP de votre machine

### Problème: "Page non trouvée"
1. **Port correct**: Vérifiez le numéro de port
2. **Service actif**: Vérifiez que l'application tourne
3. **URL complète**: Incluez `http://` au début

### Problème: "Accès lent"
1. **Réseau WiFi**: Utilisez Ethernet si possible
2. **Charge réseau**: Vérifiez la bande passante
3. **Performances serveur**: Surveillez l'usage CPU/RAM

## 📱 Interface Mobile

L'interface est responsive et fonctionne sur mobile :
- **Dashboard**: Adapté aux petits écrans
- **Navigation**: Menu hamburger sur mobile
- **Graphiques**: Redimensionnement automatique
- **Tableaux**: Défilement horizontal

## 🔄 Mise en Production

### Pour un Environnement de Production
1. **Utilisez un serveur WSGI** (Gunicorn, uWSGI)
2. **Proxy reverse** (Nginx, Apache)
3. **HTTPS** avec certificats SSL
4. **Authentification** obligatoire
5. **Monitoring des logs**

### Exemple avec Gunicorn
```bash
# Installation
pip install gunicorn

# Démarrage
gunicorn -w 4 -b 0.0.0.0:5002 app:app
```

## 📊 Monitoring de l'Accès

### Logs d'Accès
Les logs Flask montrent qui accède à l'application :
```
************ - - [10/Jul/2025 10:30:15] "GET / HTTP/1.1" 200 -
*********** - - [10/Jul/2025 10:30:20] "GET /dashboard HTTP/1.1" 200 -
```

### Surveillance des Connexions
```bash
# Voir les connexions actives
netstat -an | grep :5002

# Surveiller en temps réel
watch "netstat -an | grep :5002"
```

## ⚡ Optimisations Réseau

### Pour de Meilleures Performances
1. **Cache statique**: Servir CSS/JS via CDN
2. **Compression**: Activer gzip
3. **Keep-alive**: Connexions persistantes
4. **Mise en cache**: Headers de cache appropriés

### Configuration Réseau Optimale
```python
# Dans app.py pour la production
app.config['SEND_FILE_MAX_AGE_DEFAULT'] = 31536000  # Cache 1 an
app.config['PERMANENT_SESSION_LIFETIME'] = 1800     # Session 30 min
```

## 🆘 Support

### En Cas de Problème
1. **Vérifiez les logs** de l'application
2. **Testez en local** d'abord (127.0.0.1)
3. **Vérifiez la connectivité** réseau
4. **Consultez les logs** du pare-feu/routeur

### Commandes Utiles
```bash
# Tester la connectivité
ping IP-DU-SERVEUR

# Tester le port
telnet IP-DU-SERVEUR 5002

# Voir les processus sur le port
lsof -i :5002
```
