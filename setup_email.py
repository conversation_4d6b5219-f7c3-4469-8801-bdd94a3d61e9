#!/usr/bin/env python3
"""
Script pour configurer rapidement l'email dans le système de monitoring
"""

from app import app, db, Config
from datetime import datetime, timezone

def setup_email():
    """Configure l'email avec vos paramètres"""
    
    print("=== Configuration Email pour le Monitoring ===")
    print()
    
    # Demander les informations à l'utilisateur
    smtp_server = input("Serveur SMTP (ex: smtp.gmail.com): ").strip()
    if not smtp_server:
        smtp_server = "smtp.gmail.com"
    
    smtp_port = input("Port SMTP (défaut: 587): ").strip()
    if not smtp_port:
        smtp_port = "587"
    
    smtp_user = input("Adresse email (utilisateur SMTP): ").strip()
    if not smtp_user:
        print("❌ L'adresse email est obligatoire!")
        return False
    
    smtp_password = input("Mot de passe SMTP (ou mot de passe d'application): ").strip()
    if not smtp_password:
        print("❌ Le mot de passe est obligatoire!")
        return False
    
    email_from = input(f"Adresse expéditeur (défaut: {smtp_user}): ").strip()
    if not email_from:
        email_from = smtp_user
    
    print()
    print("=== Résumé de la configuration ===")
    print(f"Serveur SMTP: {smtp_server}")
    print(f"Port: {smtp_port}")
    print(f"Utilisateur: {smtp_user}")
    print(f"Mot de passe: {'*' * len(smtp_password)}")
    print(f"Expéditeur: {email_from}")
    print()
    
    confirm = input("Confirmer cette configuration ? (o/N): ").strip().lower()
    if confirm not in ['o', 'oui', 'y', 'yes']:
        print("❌ Configuration annulée")
        return False
    
    # Sauvegarder en base de données
    with app.app_context():
        configs = [
            ('smtp_server', smtp_server, 'Serveur SMTP pour les notifications'),
            ('smtp_port', smtp_port, 'Port du serveur SMTP'),
            ('smtp_user', smtp_user, 'Nom d\'utilisateur SMTP'),
            ('smtp_password', smtp_password, 'Mot de passe SMTP'),
            ('email_from', email_from, 'Adresse email expéditeur')
        ]
        
        for key, value, description in configs:
            config = Config.query.filter_by(key=key).first()
            if config:
                config.value = value
                config.updated_at = datetime.now(timezone.utc)
                print(f"✅ Mis à jour: {key}")
            else:
                new_config = Config(
                    key=key, 
                    value=value, 
                    description=description,
                    updated_at=datetime.now(timezone.utc)
                )
                db.session.add(new_config)
                print(f"✅ Créé: {key}")
        
        db.session.commit()
        print()
        print("🎉 Configuration email sauvegardée avec succès!")
        print()
        print("📧 Vous pouvez maintenant tester l'envoi d'email via l'interface web:")
        print("   http://127.0.0.1:5002/config")
        print()
        return True

def test_email_config():
    """Teste la configuration email"""
    with app.app_context():
        from app import send_email
        
        print("=== Test de la configuration email ===")
        result = send_email(
            "Test de configuration - Monitoring",
            "Ceci est un email de test pour vérifier que la configuration fonctionne correctement.\n\nSi vous recevez cet email, la configuration est opérationnelle!"
        )
        
        if result:
            print("✅ Email de test envoyé avec succès!")
        else:
            print("❌ Échec de l'envoi de l'email de test")
        
        return result

if __name__ == "__main__":
    print("🔧 Configuration Email - Système de Monitoring")
    print("=" * 50)
    print()
    
    choice = input("Que voulez-vous faire ?\n1. Configurer l'email\n2. Tester la configuration actuelle\n3. Les deux\nChoix (1/2/3): ").strip()
    
    if choice == "1":
        setup_email()
    elif choice == "2":
        test_email_config()
    elif choice == "3":
        if setup_email():
            print()
            input("Appuyez sur Entrée pour tester la configuration...")
            test_email_config()
    else:
        print("❌ Choix invalide")
