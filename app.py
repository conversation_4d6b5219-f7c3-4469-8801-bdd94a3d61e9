from flask import Flask, render_template, request, redirect, url_for, session
from flask_sqlalchemy import SQLAlchemy
import subprocess
import platform
import ipaddress
from datetime import datetime, timedelta, timezone
from pysnmp.hlapi import *
import concurrent.futures
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import json

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///monitoring.db'
app.secret_key = "secret_key_super_secrète"  # Change-la en vraie clé secrète
db = SQLAlchemy(app)

# Host Groups (like Zabbix)
class HostGroup(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, unique=True)
    description = db.Column(db.String(500), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# Templates (like Zabbix)
class Template(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, unique=True)
    description = db.Column(db.String(500), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# Enhanced Machine/Host model
class Machine(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    ip = db.Column(db.String(100), nullable=False)
    name = db.Column(db.String(200), nullable=False)
    status = db.Column(db.String(10), nullable=False)
    cpu_load = db.Column(db.Integer, nullable=True)
    memory_usage = db.Column(db.Integer, nullable=True)  # Memory usage percentage
    disk_usage = db.Column(db.Integer, nullable=True)    # Disk usage percentage
    uptime = db.Column(db.Integer, nullable=True)        # Uptime in seconds
    description = db.Column(db.String(500), nullable=True)
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    host_group_id = db.Column(db.Integer, db.ForeignKey('host_group.id'), nullable=True)
    template_id = db.Column(db.Integer, db.ForeignKey('template.id'), nullable=True)
    snmp_community = db.Column(db.String(100), default='public')
    snmp_version = db.Column(db.String(10), default='2c')
    monitoring_enabled = db.Column(db.Boolean, default=True)

    # Relationships
    host_group = db.relationship('HostGroup', backref='machines')
    template = db.relationship('Template', backref='machines')

# Enhanced Alert model with severity levels
class Alert(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    ip = db.Column(db.String(100), nullable=False)
    name = db.Column(db.String(200), nullable=False)
    status = db.Column(db.String(10), nullable=False)
    message = db.Column(db.String(500), nullable=False)
    severity = db.Column(db.String(20), default='warning')  # info, warning, average, high, disaster
    timestamp = db.Column(db.DateTime, default=lambda: datetime.now(timezone.utc))
    acknowledged = db.Column(db.Boolean, default=False)
    ack_timestamp = db.Column(db.DateTime, nullable=True)
    ack_user = db.Column(db.String(100), nullable=True)
    resolved = db.Column(db.Boolean, default=False)
    resolved_timestamp = db.Column(db.DateTime, nullable=True)

# Items (monitoring metrics like Zabbix)
class Item(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    key = db.Column(db.String(200), nullable=False)  # e.g., 'system.cpu.load', 'vm.memory.size'
    oid = db.Column(db.String(200), nullable=True)   # SNMP OID
    value_type = db.Column(db.String(20), default='numeric')  # numeric, text, log
    units = db.Column(db.String(50), nullable=True)  # %, B, bps, etc.
    description = db.Column(db.String(500), nullable=True)
    template_id = db.Column(db.Integer, db.ForeignKey('template.id'), nullable=True)
    active = db.Column(db.Boolean, default=True)

    # Relationship
    template = db.relationship('Template', backref='items')

# Item History (stores collected values)
class ItemHistory(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    item_id = db.Column(db.Integer, db.ForeignKey('item.id'), nullable=False)
    machine_id = db.Column(db.Integer, db.ForeignKey('machine.id'), nullable=False)
    value = db.Column(db.String(500), nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    item = db.relationship('Item', backref='history')
    machine = db.relationship('Machine', backref='item_history')

previous_status = {}

# Enhanced AlertRule (Triggers in Zabbix)
class AlertRule(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    metric = db.Column(db.String(100), nullable=False)
    operator = db.Column(db.String(10), nullable=False)
    value = db.Column(db.String(100), nullable=False)
    message = db.Column(db.String(500), nullable=False)
    severity = db.Column(db.String(20), default='warning')  # info, warning, average, high, disaster
    active = db.Column(db.Boolean, default=True)
    template_id = db.Column(db.Integer, db.ForeignKey('template.id'), nullable=True)
    host_group_id = db.Column(db.Integer, db.ForeignKey('host_group.id'), nullable=True)

    # Relationships
    template = db.relationship('Template', backref='alert_rules')
    host_group = db.relationship('HostGroup', backref='alert_rules')

# User management
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), nullable=False, unique=True)
    email = db.Column(db.String(200), nullable=False, unique=True)
    password_hash = db.Column(db.String(200), nullable=False)
    role = db.Column(db.String(50), default='user')  # admin, user, viewer
    active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)

# Configuration settings
class Config(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), nullable=False, unique=True)
    value = db.Column(db.Text, nullable=False)
    description = db.Column(db.String(500), nullable=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow)

def ping(ip):
    param = "-n" if platform.system().lower() == "windows" else "-c"
    command = ["ping", param, "1", ip]
    result = subprocess.run(command, stdout=subprocess.DEVNULL)
    return result.returncode == 0

def snmp_get(ip, oid='*******.*******.0'):
    command = ['snmpget', '-v', '2c', '-c', 'public', '-t', '1', '-r', '1', ip, oid]
    try:
        result = subprocess.run(command, capture_output=True, text=True, timeout=2)
        if result.returncode != 0 or "No Response" in result.stdout or "Timeout" in result.stdout:
            return None
        
        parts = result.stdout.split(':', 3)
        if len(parts) > 1:
            value = parts[-1].strip()
            if value.startswith('"') and value.endswith('"'):
                return value[1:-1]
            return value
        return None
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return None
    except Exception as e:
        print(f"[ERROR] Command-line snmpget failed for IP {ip} with OID {oid}: {e}")
        return None

def get_cpu_load(ip, description):
    if not description:
        return None

    desc_lower = description.lower()

    oid = None

    if 'linux' in desc_lower or 'x86_64' in desc_lower:
        oid = '*******.4.1.2021.11.11.0'  # idle CPU percentage
        idle_str = snmp_get(ip, oid)
        try:
            return 100 - int(idle_str)
        except (ValueError, TypeError):
            return None
    elif 'windows' in desc_lower:
        oid = '*******.********.3.1.2.196608'
    elif 'cisco' in desc_lower:
        oid = '*******.*******.*********.1.5'

    if oid:
        val = snmp_get(ip, oid)
        try:
            return int(val)
        except (ValueError, TypeError):
            return None

    return None

def get_memory_usage(ip, description):
    """Get memory usage percentage"""
    if not description:
        return None

    desc_lower = description.lower()

    if 'linux' in desc_lower or 'x86_64' in desc_lower:
        # Get total and available memory
        total_mem = snmp_get(ip, '*******.4.1.2021.4.5.0')  # memTotalReal
        avail_mem = snmp_get(ip, '*******.4.1.2021.4.6.0')  # memAvailReal
        try:
            total = int(total_mem)
            avail = int(avail_mem)
            used = total - avail
            return int((used / total) * 100)
        except (ValueError, TypeError):
            return None
    elif 'windows' in desc_lower:
        # Windows memory usage
        total_mem = snmp_get(ip, '*******.********.2.0')  # hrMemorySize
        try:
            # This is a simplified approach for Windows
            return None  # Would need more complex SNMP walking
        except (ValueError, TypeError):
            return None

    return None

def get_disk_usage(ip, description):
    """Get disk usage percentage for root partition"""
    if not description:
        return None

    desc_lower = description.lower()

    if 'linux' in desc_lower or 'x86_64' in desc_lower:
        # Get disk usage for root partition
        disk_total = snmp_get(ip, '*******.4.1.2021.*******')  # dskTotal
        disk_used = snmp_get(ip, '*******.4.1.2021.*******')   # dskUsed
        try:
            total = int(disk_total)
            used = int(disk_used)
            return int((used / total) * 100)
        except (ValueError, TypeError):
            return None

    return None

def get_uptime(ip):
    """Get system uptime in seconds"""
    uptime_str = snmp_get(ip, '*******.*******.0')  # sysUpTime
    try:
        # sysUpTime is in hundredths of a second
        return int(uptime_str) // 100
    except (ValueError, TypeError):
        return None

def scan_ip(ip_str):
    is_up = ping(ip_str)
    name = None
    cpu_load = None
    memory_usage = None
    disk_usage = None
    uptime = None
    description = None

    if is_up:
        description = snmp_get(ip_str, oid='*******.*******.0')
        name = snmp_get(ip_str)  # sysName

        if description:
            desc_lower = description.lower()

            NON_SERVER_KEYWORDS = ['printer', 'imprimante', 'epson', 'hp', 'canon', 'lexmark', 'xerox', 'scanner', 'plotter']
            SERVER_KEYWORDS = ['server', 'ubuntu', 'debian', 'centos', 'red hat', 'windows', 'linux', 'srv']

            if any(non_kw in desc_lower for non_kw in NON_SERVER_KEYWORDS):
                print(f"[INFO] {ip_str} ignoré (imprimante ou périphérique - description : {description})")
                return None

            if not any(srv_kw in desc_lower for srv_kw in SERVER_KEYWORDS):
                print(f"[INFO] {ip_str} ignoré (pas identifié comme un serveur - description : {description})")
                return None

            # Collect all metrics
            cpu_load = get_cpu_load(ip_str, description)
            memory_usage = get_memory_usage(ip_str, description)
            disk_usage = get_disk_usage(ip_str, description)
            uptime = get_uptime(ip_str)

        if not description and not name:
            print(f"[INFO] Pas de réponse SNMP de {ip_str}. Appareil hors ligne ou SNMP non configuré.")

    return {
        "ip": ip_str,
        "status": "up" if is_up else "down",
        "name": name if name else "N/A",
        "cpu_load": cpu_load,
        "memory_usage": memory_usage,
        "disk_usage": disk_usage,
        "uptime": uptime,
        "description": description if description else "N/A"
    }

def scan_network(network_cidr):
    network = ipaddress.ip_network(network_cidr, strict=False)
    ips = [str(ip) for ip in network.hosts()]
    with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
        results = executor.map(scan_ip, ips)
    return list(results)

def get_config_value(key, default=""):
    """Get configuration value from database"""
    config = Config.query.filter_by(key=key).first()
    return config.value if config else default

def send_email(subject, message):
    """Send email using configuration from database"""
    try:
        # Get email configuration from database
        smtp_server = get_config_value('smtp_server')
        smtp_port = get_config_value('smtp_port', '587')
        smtp_user = get_config_value('smtp_user')
        smtp_password = get_config_value('smtp_password')
        email_from = get_config_value('email_from')

        if not all([smtp_server, smtp_user, smtp_password, email_from]):
            print("Configuration email incomplète - email non envoyé")
            return False

        msg = MIMEMultipart()
        msg['From'] = email_from
        msg['To'] = smtp_user  # Send to admin user
        msg['Subject'] = subject
        msg.attach(MIMEText(message, 'plain'))

        server = smtplib.SMTP(smtp_server, int(smtp_port))
        server.starttls()
        server.login(smtp_user, smtp_password)
        server.sendmail(email_from, smtp_user, msg.as_string())
        server.quit()
        print(f"Email envoyé : {subject}")
        return True
    except Exception as e:
        print(f"Erreur envoi email : {e}")
        return False

@app.route("/", methods=["GET", "POST"])
def index():
    alerts = []
    error = None
    global previous_status

    if request.method == "POST":
        network_cidr = request.form.get("network_cidr")
        try:
            ipaddress.ip_network(network_cidr, strict=False)
            scanned = scan_network(network_cidr)
            scan_timestamp = datetime.now(timezone.utc)

            for machine in scanned:
                if machine is None:
                    continue  # Ignore non-servers

                entry = Machine(
                    ip=machine["ip"],
                    name=machine["name"],
                    status=machine["status"],
                    cpu_load=machine["cpu_load"],
                    memory_usage=machine["memory_usage"],
                    disk_usage=machine["disk_usage"],
                    uptime=machine["uptime"],
                    timestamp=scan_timestamp,
                    description=machine["description"]
                )
                db.session.add(entry)
            db.session.commit()

            session['last_scan_time'] = scan_timestamp.isoformat()
            session['last_ip_searched'] = network_cidr

            # Gestion alertes changement d'état simple
            current_status = {m["ip"]: m["status"] for m in scanned if m is not None}
            for machine in scanned:
                if machine is None:
                    continue
                ip = machine["ip"]
                curr = machine["status"]
                prev = previous_status.get(ip)

                if prev and prev != curr:
                    if curr == "down":
                        alert_msg = f"⚠️ {ip} ({machine['name']}) est maintenant HORS LIGNE."
                        severity = "high"
                    else:
                        alert_msg = f"✅ {ip} ({machine['name']}) est de nouveau EN LIGNE."
                        severity = "info"

                    last_alert = Alert.query.filter_by(ip=ip, status=curr, acknowledged=False).order_by(Alert.timestamp.desc()).first()
                    if not last_alert or (datetime.now(timezone.utc) - last_alert.timestamp).total_seconds() > 300:
                        new_alert = Alert(ip=ip, name=machine['name'], status=curr, message=alert_msg, severity=severity)
                        db.session.add(new_alert)
                        db.session.commit()
                        send_email(
                            f"[{severity.upper()}] Alerte Monitoring - Machine " + ("hors ligne" if curr == "down" else "en ligne"),
                            alert_msg
                        )
                        alerts.append(alert_msg)

            previous_status = current_status

            # Appliquer règles personnalisées
            from jinja2 import Template
            rules = AlertRule.query.filter_by(active=True).all()

            for machine in scanned:
                if machine is None:
                    continue
                context = {
                    "ip": machine["ip"],
                    "status": machine["status"],
                    "name": machine["name"],
                    "cpu_load": machine["cpu_load"],
                    "memory_usage": machine["memory_usage"],
                    "disk_usage": machine["disk_usage"],
                    "uptime": machine["uptime"],
                    "description": machine["description"]
                }
                for rule in rules:
                    metric_value = context.get(rule.metric)
                    if metric_value is None:
                        continue

                    match = False
                    is_numeric_comparison = rule.operator in ('>', '<', '>=', '<=')

                    if is_numeric_comparison:
                        try:
                            metric_val_num = float(metric_value)
                            rule_val_num = float(rule.value)
                            if rule.operator == '>' and metric_val_num > rule_val_num: match = True
                            elif rule.operator == '<' and metric_val_num < rule_val_num: match = True
                            elif rule.operator == '>=' and metric_val_num >= rule_val_num: match = True
                            elif rule.operator == '<=' and metric_val_num <= rule_val_num: match = True
                        except (ValueError, TypeError):
                            pass
                    else:
                        if rule.operator == '==' and str(metric_value) == rule.value:
                            match = True
                        elif rule.operator == '!=' and str(metric_value) != rule.value:
                            match = True

                    if match:
                        template = Template(rule.message)
                        alert_msg = template.render(**context)

                        last_alert = Alert.query.filter_by(ip=machine["ip"], message=alert_msg, acknowledged=False)\
                                                 .order_by(Alert.timestamp.desc()).first()

                        if not last_alert or (datetime.now(timezone.utc) - last_alert.timestamp).total_seconds() > 300:
                            new_alert = Alert(ip=machine["ip"], name=machine["name"], status=machine["status"],
                                            message=alert_msg, severity=rule.severity)
                            db.session.add(new_alert)
                            db.session.commit()

                            alerts.append(alert_msg)
                            send_email(f"[{rule.severity.upper()}] Alerte Monitoring: {rule.name}", alert_msg)

            return render_template("index.html", results=scanned, alerts=alerts, error=error, ip_input=network_cidr)

        except ValueError:
            error = "Format invalide. Exemple attendu : ***********/24"

    results = []
    ip_input = ""
    if 'last_scan_time' in session:
        scan_time = datetime.fromisoformat(session['last_scan_time'])
        time_start = scan_time - timedelta(seconds=10)
        time_end = scan_time + timedelta(seconds=10)
        results = Machine.query.filter(Machine.timestamp.between(time_start, time_end)).all()

    if 'last_ip_searched' in session:
        ip_input = session['last_ip_searched']

    return render_template("index.html", results=results, alerts=alerts, error=error, ip_input=ip_input)

@app.route("/historique")
def historique():
    machines = Machine.query.order_by(Machine.timestamp.desc()).limit(250).all()
    return render_template("historique.html", machines=machines)
@app.route("/machine/<ip>")
def machine_detail(ip):
    history = Machine.query.filter(Machine.ip == ip).order_by(Machine.timestamp.asc()).all()

    last_entry = Machine.query.filter_by(ip=ip).order_by(Machine.timestamp.desc()).first()
    machine_name = last_entry.name if last_entry else "N/A"
    machine_description = last_entry.description if last_entry else "N/A"

    chart_labels = [h.timestamp.strftime('%Y-%m-%d %H:%M:%S') for h in history]
    chart_cpu_data = [h.cpu_load if h.cpu_load is not None else 0 for h in history]
    chart_memory_data = [h.memory_usage if h.memory_usage is not None else 0 for h in history]
    chart_disk_data = [h.disk_usage if h.disk_usage is not None else 0 for h in history]

    return render_template("machine_detail.html",
                           machine_ip=ip,
                           machine_name=machine_name,
                           machine_description=machine_description,
                           history=history,
                           chart_labels=chart_labels,
                           chart_cpu_data=chart_cpu_data,
                           chart_memory_data=chart_memory_data,
                           chart_disk_data=chart_disk_data)


@app.route("/alerts")
def alerts():
    alerts = Alert.query.order_by(Alert.timestamp.desc()).limit(100).all()
    return render_template("alerts.html", alerts=alerts)

@app.route("/alert/acknowledge/<int:alert_id>", methods=["POST"])
def acknowledge_alert(alert_id):
    alert = Alert.query.get_or_404(alert_id)
    alert.acknowledged = True
    alert.ack_timestamp = datetime.now(timezone.utc)
    alert.ack_user = "admin"
    db.session.commit()
    return redirect(url_for('alerts'))

from sqlalchemy import func

@app.route("/dashboard")
def dashboard():
    now = datetime.now(timezone.utc)
    start_time = now - timedelta(hours=24)

    results = db.session.query(
        func.strftime('%Y-%m-%d %H:00:00', Machine.timestamp).label('hour'),
        func.avg(Machine.cpu_load).label('avg_cpu')
    ).filter(
        Machine.timestamp >= start_time,
        Machine.cpu_load != None
    ).group_by('hour').order_by('hour').all()

    cpu_labels = [r.hour for r in results]
    cpu_data = [round(r.avg_cpu, 2) for r in results]

    total_machines = Machine.query.count()
    up_count = Machine.query.filter_by(status="up").count()
    down_count = total_machines - up_count
    alert_count = Alert.query.filter(Alert.timestamp >= now.replace(hour=0, minute=0, second=0)).count()

    # Get top CPU usage hosts
    top_cpu = Machine.query.filter(Machine.cpu_load.isnot(None)).order_by(Machine.cpu_load.desc()).limit(10).all()

    # Get unacknowledged alerts
    unack_alerts = Alert.query.filter_by(acknowledged=False).order_by(Alert.timestamp.desc()).limit(10).all()

    return render_template("dashboard.html",
                           total_machines=total_machines,
                           up_count=up_count,
                           down_count=down_count,
                           alert_count=alert_count,
                           cpu_labels=json.dumps(cpu_labels),
                           cpu_data=json.dumps(cpu_data),
                           top_cpu=top_cpu,
                           alerts=unack_alerts)

@app.route("/api/dashboard/stats")
def dashboard_stats_api():
    """API endpoint for real-time dashboard statistics"""
    now = datetime.now(timezone.utc)

    # Get basic stats
    total_machines = Machine.query.count()
    up_count = Machine.query.filter_by(status="up").count()
    down_count = total_machines - up_count
    alert_count = Alert.query.filter(Alert.timestamp >= now.replace(hour=0, minute=0, second=0)).count()
    unack_alert_count = Alert.query.filter_by(acknowledged=False).count()

    # Get recent alerts
    recent_alerts = Alert.query.filter_by(acknowledged=False).order_by(Alert.timestamp.desc()).limit(5).all()

    # Calculate system status
    if down_count > total_machines * 0.5:
        system_status = "critical"
        status_text = "🔴 Système critique"
    elif down_count > 0 or unack_alert_count > 10:
        system_status = "warning"
        status_text = "🟡 Attention requise"
    else:
        system_status = "ok"
        status_text = "🟢 Système opérationnel"

    return {
        "total_machines": total_machines,
        "up_count": up_count,
        "down_count": down_count,
        "alert_count": alert_count,
        "unack_alert_count": unack_alert_count,
        "system_status": system_status,
        "status_text": status_text,
        "recent_alerts": [
            {
                "id": alert.id,
                "ip": alert.ip,
                "name": alert.name,
                "message": alert.message,
                "severity": alert.severity,
                "timestamp": alert.timestamp.strftime('%d/%m/%Y %H:%M:%S')
            } for alert in recent_alerts
        ],
        "timestamp": now.strftime('%d/%m/%Y %H:%M:%S')
    }


@app.route("/rules", methods=["GET", "POST"])
def manage_rules():
    if request.method == "POST":
        name = request.form.get("name")
        metric = request.form.get("metric")
        operator = request.form.get("operator")
        value = request.form.get("value")
        message = request.form.get("message")
        severity = request.form.get("severity", "warning")
        new_rule = AlertRule(name=name, metric=metric, operator=operator, value=value, message=message, severity=severity)
        db.session.add(new_rule)
        db.session.commit()
        return redirect(url_for("manage_rules"))

    rules = AlertRule.query.all()
    return render_template("rules.html", rules=rules)

@app.route("/rules/delete/<int:rule_id>", methods=["POST"])
def delete_rule(rule_id):
    rule = AlertRule.query.get_or_404(rule_id)
    db.session.delete(rule)
    db.session.commit()
    return redirect(url_for("manage_rules"))

@app.route("/rules/toggle/<int:rule_id>", methods=["POST"])
def toggle_rule(rule_id):
    rule = AlertRule.query.get_or_404(rule_id)
    rule.active = not rule.active
    db.session.commit()
    return redirect(url_for("manage_rules"))

@app.route("/rules/edit/<int:rule_id>", methods=["GET", "POST"])
def edit_rule(rule_id):
    rule = AlertRule.query.get_or_404(rule_id)
    if request.method == "POST":
        rule.name = request.form.get("name")
        rule.metric = request.form.get("metric")
        rule.operator = request.form.get("operator")
        rule.value = request.form.get("value")
        rule.message = request.form.get("message")
        rule.severity = request.form.get("severity", "warning")
        db.session.commit()
        return redirect(url_for("manage_rules"))
    return render_template("edit_rule.html", rule=rule)


# Host Groups Management
@app.route("/hostgroups", methods=["GET", "POST"])
def manage_hostgroups():
    if request.method == "POST":
        name = request.form.get("name")
        description = request.form.get("description", "")
        new_group = HostGroup(name=name, description=description)
        db.session.add(new_group)
        db.session.commit()
        return redirect(url_for("manage_hostgroups"))

    groups = HostGroup.query.all()
    return render_template("hostgroups.html", groups=groups)

@app.route("/hostgroups/delete/<int:group_id>", methods=["POST"])
def delete_hostgroup(group_id):
    group = HostGroup.query.get_or_404(group_id)
    # Update machines to remove group assignment
    Machine.query.filter_by(host_group_id=group_id).update({Machine.host_group_id: None})
    db.session.delete(group)
    db.session.commit()
    return redirect(url_for("manage_hostgroups"))

@app.route("/hostgroups/edit/<int:group_id>", methods=["GET", "POST"])
def edit_hostgroup(group_id):
    group = HostGroup.query.get_or_404(group_id)
    if request.method == "POST":
        group.name = request.form.get("name")
        group.description = request.form.get("description", "")
        db.session.commit()
        return redirect(url_for("manage_hostgroups"))
    return render_template("edit_hostgroup.html", group=group)

# Templates Management
@app.route("/templates", methods=["GET", "POST"])
def manage_templates():
    if request.method == "POST":
        name = request.form.get("name")
        description = request.form.get("description", "")
        new_template = Template(name=name, description=description)
        db.session.add(new_template)
        db.session.commit()
        return redirect(url_for("manage_templates"))

    templates = Template.query.all()
    return render_template("templates.html", templates=templates)

@app.route("/templates/delete/<int:template_id>", methods=["POST"])
def delete_template(template_id):
    template = Template.query.get_or_404(template_id)
    # Update machines to remove template assignment
    Machine.query.filter_by(template_id=template_id).update({Machine.template_id: None})
    db.session.delete(template)
    db.session.commit()
    return redirect(url_for("manage_templates"))

@app.route("/templates/edit/<int:template_id>", methods=["GET", "POST"])
def edit_template(template_id):
    template = Template.query.get_or_404(template_id)
    if request.method == "POST":
        template.name = request.form.get("name")
        template.description = request.form.get("description", "")
        db.session.commit()
        return redirect(url_for("manage_templates"))
    return render_template("edit_template.html", template=template)

# Host Management
@app.route("/hosts")
def manage_hosts():
    hosts = Machine.query.order_by(Machine.timestamp.desc()).limit(100).all()
    groups = HostGroup.query.all()
    templates = Template.query.all()
    return render_template("hosts.html", hosts=hosts, groups=groups, templates=templates)

@app.route("/hosts/assign/<int:machine_id>", methods=["POST"])
def assign_host_group_template(machine_id):
    machine = Machine.query.get_or_404(machine_id)
    group_id = request.form.get("group_id")
    template_id = request.form.get("template_id")

    if group_id and group_id != "":
        machine.host_group_id = int(group_id) if group_id != "none" else None
    if template_id and template_id != "":
        machine.template_id = int(template_id) if template_id != "none" else None

    db.session.commit()
    return redirect(url_for("manage_hosts"))

# Configuration Management
@app.route("/config", methods=["GET", "POST"])
def manage_config():
    if request.method == "POST":
        # Update configuration values
        for key, value in request.form.items():
            if key.startswith('config_'):
                config_key = key.replace('config_', '')
                config = Config.query.filter_by(key=config_key).first()
                if config:
                    config.value = value
                    config.updated_at = datetime.now(timezone.utc)

        db.session.commit()
        return redirect(url_for("manage_config"))

    # Get all configuration items
    configs = Config.query.all()
    config_dict = {config.key: config for config in configs}

    return render_template("config.html", configs=config_dict)

@app.route("/config/test-email", methods=["POST"])
def test_email():
    """Test email configuration"""
    try:
        # Get current email settings
        smtp_server = Config.query.filter_by(key='smtp_server').first()
        smtp_port = Config.query.filter_by(key='smtp_port').first()
        smtp_user = Config.query.filter_by(key='smtp_user').first()
        smtp_password = Config.query.filter_by(key='smtp_password').first()
        email_from = Config.query.filter_by(key='email_from').first()

        if not all([smtp_server, smtp_port, smtp_user, smtp_password, email_from]):
            return {"success": False, "message": "Configuration email incomplète"}

        # Send test email
        msg = MIMEMultipart()
        msg['From'] = email_from.value
        msg['To'] = smtp_user.value  # Send to self for testing
        msg['Subject'] = "Test de configuration email - Monitoring"

        body = """
        Ceci est un email de test pour vérifier la configuration SMTP.

        Si vous recevez cet email, la configuration est correcte.

        Système de monitoring réseau
        """
        msg.attach(MIMEText(body, 'plain'))

        server = smtplib.SMTP(smtp_server.value, int(smtp_port.value))
        server.starttls()
        server.login(smtp_user.value, smtp_password.value)
        server.sendmail(email_from.value, smtp_user.value, msg.as_string())
        server.quit()

        return {"success": True, "message": "Email de test envoyé avec succès"}

    except Exception as e:
        return {"success": False, "message": f"Erreur: {str(e)}"}

# Data Retention and Cleanup (Housekeeping)
@app.route("/housekeeping", methods=["GET", "POST"])
def housekeeping():
    if request.method == "POST":
        action = request.form.get("action")

        if action == "cleanup_old_data":
            result = cleanup_old_data()
            return {"success": True, "message": result}
        elif action == "cleanup_alerts":
            result = cleanup_old_alerts()
            return {"success": True, "message": result}
        elif action == "cleanup_history":
            result = cleanup_old_history()
            return {"success": True, "message": result}

    # Get statistics
    stats = get_database_stats()
    return render_template("housekeeping.html", stats=stats)

def cleanup_old_data():
    """Clean up old data based on retention policy"""
    retention_days = int(get_config_value('data_retention_days', '30'))
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=retention_days)

    # Clean up old machine records
    old_machines = Machine.query.filter(Machine.timestamp < cutoff_date).count()
    Machine.query.filter(Machine.timestamp < cutoff_date).delete()

    # Clean up old item history
    old_history = ItemHistory.query.filter(ItemHistory.timestamp < cutoff_date).count()
    ItemHistory.query.filter(ItemHistory.timestamp < cutoff_date).delete()

    db.session.commit()

    return f"Supprimé {old_machines} enregistrements de machines et {old_history} enregistrements d'historique (plus de {retention_days} jours)"

def cleanup_old_alerts():
    """Clean up acknowledged alerts older than retention period"""
    retention_days = int(get_config_value('data_retention_days', '30'))
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=retention_days)

    # Only delete acknowledged alerts
    old_alerts = Alert.query.filter(
        Alert.acknowledged == True,
        Alert.timestamp < cutoff_date
    ).count()

    Alert.query.filter(
        Alert.acknowledged == True,
        Alert.timestamp < cutoff_date
    ).delete()

    db.session.commit()

    return f"Supprimé {old_alerts} alertes acquittées anciennes"

def cleanup_old_history():
    """Clean up old item history keeping only recent data"""
    retention_days = int(get_config_value('data_retention_days', '30'))
    cutoff_date = datetime.now(timezone.utc) - timedelta(days=retention_days)

    old_history = ItemHistory.query.filter(ItemHistory.timestamp < cutoff_date).count()
    ItemHistory.query.filter(ItemHistory.timestamp < cutoff_date).delete()

    db.session.commit()

    return f"Supprimé {old_history} enregistrements d'historique d'items"

def get_database_stats():
    """Get database statistics for housekeeping page"""
    now = datetime.now(timezone.utc)
    retention_days = int(get_config_value('data_retention_days', '30'))
    cutoff_date = now - timedelta(days=retention_days)

    stats = {
        'total_machines': Machine.query.count(),
        'old_machines': Machine.query.filter(Machine.timestamp < cutoff_date).count(),
        'total_alerts': Alert.query.count(),
        'old_acknowledged_alerts': Alert.query.filter(
            Alert.acknowledged == True,
            Alert.timestamp < cutoff_date
        ).count(),
        'unacknowledged_alerts': Alert.query.filter(Alert.acknowledged == False).count(),
        'total_history': ItemHistory.query.count(),
        'old_history': ItemHistory.query.filter(ItemHistory.timestamp < cutoff_date).count(),
        'retention_days': retention_days,
        'cutoff_date': cutoff_date.strftime('%d/%m/%Y %H:%M')
    }

    return stats

# Automatic cleanup scheduler (can be called by cron job)
@app.route("/api/housekeeping/auto", methods=["POST"])
def auto_housekeeping():
    """Automatic housekeeping endpoint for scheduled cleanup"""
    try:
        result_data = cleanup_old_data()
        result_alerts = cleanup_old_alerts()
        result_history = cleanup_old_history()

        return {
            "success": True,
            "message": "Nettoyage automatique effectué",
            "details": {
                "data": result_data,
                "alerts": result_alerts,
                "history": result_history
            }
        }
    except Exception as e:
        return {"success": False, "message": f"Erreur lors du nettoyage: {str(e)}"}

# Export/Import Functionality
@app.route("/export-import")
def export_import():
    return render_template("export_import.html")

@app.route("/export/<export_type>")
def export_data(export_type):
    """Export configuration data"""
    try:
        if export_type == "hostgroups":
            data = export_hostgroups()
        elif export_type == "templates":
            data = export_templates()
        elif export_type == "rules":
            data = export_rules()
        elif export_type == "config":
            data = export_config()
        elif export_type == "all":
            data = export_all()
        else:
            return {"error": "Type d'export invalide"}, 400

        from flask import Response
        import json

        response = Response(
            json.dumps(data, indent=2, ensure_ascii=False),
            mimetype='application/json',
            headers={'Content-Disposition': f'attachment; filename=monitoring_{export_type}_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'}
        )
        return response

    except Exception as e:
        return {"error": f"Erreur lors de l'export: {str(e)}"}, 500

@app.route("/import", methods=["POST"])
def import_data():
    """Import configuration data"""
    try:
        if 'file' not in request.files:
            return {"success": False, "message": "Aucun fichier sélectionné"}

        file = request.files['file']
        if file.filename == '':
            return {"success": False, "message": "Aucun fichier sélectionné"}

        if not file.filename.endswith('.json'):
            return {"success": False, "message": "Le fichier doit être au format JSON"}

        import json
        data = json.load(file)

        result = import_configuration(data)
        return {"success": True, "message": result}

    except Exception as e:
        return {"success": False, "message": f"Erreur lors de l'import: {str(e)}"}

def export_hostgroups():
    """Export host groups"""
    groups = HostGroup.query.all()
    return {
        "type": "hostgroups",
        "version": "1.0",
        "exported_at": datetime.now(timezone.utc).isoformat(),
        "data": [
            {
                "name": group.name,
                "description": group.description,
                "created_at": group.created_at.isoformat() if group.created_at else None
            }
            for group in groups
        ]
    }

def export_templates():
    """Export templates with their items and rules"""
    templates = Template.query.all()
    return {
        "type": "templates",
        "version": "1.0",
        "exported_at": datetime.now(timezone.utc).isoformat(),
        "data": [
            {
                "name": template.name,
                "description": template.description,
                "created_at": template.created_at.isoformat() if template.created_at else None,
                "items": [
                    {
                        "name": item.name,
                        "key": item.key,
                        "oid": item.oid,
                        "value_type": item.value_type,
                        "units": item.units,
                        "description": item.description,
                        "active": item.active
                    }
                    for item in template.items
                ],
                "alert_rules": [
                    {
                        "name": rule.name,
                        "metric": rule.metric,
                        "operator": rule.operator,
                        "value": rule.value,
                        "message": rule.message,
                        "severity": rule.severity,
                        "active": rule.active
                    }
                    for rule in template.alert_rules
                ]
            }
            for template in templates
        ]
    }

def export_rules():
    """Export alert rules"""
    rules = AlertRule.query.all()
    return {
        "type": "rules",
        "version": "1.0",
        "exported_at": datetime.now(timezone.utc).isoformat(),
        "data": [
            {
                "name": rule.name,
                "metric": rule.metric,
                "operator": rule.operator,
                "value": rule.value,
                "message": rule.message,
                "severity": rule.severity,
                "active": rule.active,
                "template_name": rule.template.name if rule.template else None,
                "hostgroup_name": rule.host_group.name if rule.host_group else None
            }
            for rule in rules
        ]
    }

def export_config():
    """Export system configuration"""
    configs = Config.query.all()
    return {
        "type": "config",
        "version": "1.0",
        "exported_at": datetime.now(timezone.utc).isoformat(),
        "data": [
            {
                "key": config.key,
                "value": config.value,
                "description": config.description
            }
            for config in configs
        ]
    }

def export_all():
    """Export all configuration data"""
    return {
        "type": "all",
        "version": "1.0",
        "exported_at": datetime.now(timezone.utc).isoformat(),
        "hostgroups": export_hostgroups()["data"],
        "templates": export_templates()["data"],
        "rules": export_rules()["data"],
        "config": export_config()["data"]
    }

def import_configuration(data):
    """Import configuration data from JSON"""
    imported_count = 0

    try:
        # Import host groups
        if "hostgroups" in data:
            for group_data in data["hostgroups"]:
                existing = HostGroup.query.filter_by(name=group_data["name"]).first()
                if not existing:
                    new_group = HostGroup(
                        name=group_data["name"],
                        description=group_data.get("description", "")
                    )
                    db.session.add(new_group)
                    imported_count += 1

        # Import templates
        if "templates" in data:
            for template_data in data["templates"]:
                existing = Template.query.filter_by(name=template_data["name"]).first()
                if not existing:
                    new_template = Template(
                        name=template_data["name"],
                        description=template_data.get("description", "")
                    )
                    db.session.add(new_template)
                    db.session.flush()  # Get the ID

                    # Import items for this template
                    for item_data in template_data.get("items", []):
                        new_item = Item(
                            name=item_data["name"],
                            key=item_data["key"],
                            oid=item_data.get("oid"),
                            value_type=item_data.get("value_type", "numeric"),
                            units=item_data.get("units"),
                            description=item_data.get("description"),
                            template_id=new_template.id,
                            active=item_data.get("active", True)
                        )
                        db.session.add(new_item)

                    # Import alert rules for this template
                    for rule_data in template_data.get("alert_rules", []):
                        new_rule = AlertRule(
                            name=rule_data["name"],
                            metric=rule_data["metric"],
                            operator=rule_data["operator"],
                            value=rule_data["value"],
                            message=rule_data["message"],
                            severity=rule_data.get("severity", "warning"),
                            template_id=new_template.id,
                            active=rule_data.get("active", True)
                        )
                        db.session.add(new_rule)

                    imported_count += 1

        # Import standalone rules
        if "rules" in data:
            for rule_data in data["rules"]:
                existing = AlertRule.query.filter_by(name=rule_data["name"]).first()
                if not existing:
                    # Find template and hostgroup by name if specified
                    template_id = None
                    hostgroup_id = None

                    if rule_data.get("template_name"):
                        template = Template.query.filter_by(name=rule_data["template_name"]).first()
                        if template:
                            template_id = template.id

                    if rule_data.get("hostgroup_name"):
                        hostgroup = HostGroup.query.filter_by(name=rule_data["hostgroup_name"]).first()
                        if hostgroup:
                            hostgroup_id = hostgroup.id

                    new_rule = AlertRule(
                        name=rule_data["name"],
                        metric=rule_data["metric"],
                        operator=rule_data["operator"],
                        value=rule_data["value"],
                        message=rule_data["message"],
                        severity=rule_data.get("severity", "warning"),
                        template_id=template_id,
                        host_group_id=hostgroup_id,
                        active=rule_data.get("active", True)
                    )
                    db.session.add(new_rule)
                    imported_count += 1

        # Import configuration
        if "config" in data:
            for config_data in data["config"]:
                existing = Config.query.filter_by(key=config_data["key"]).first()
                if existing:
                    existing.value = config_data["value"]
                    existing.updated_at = datetime.now(timezone.utc)
                else:
                    new_config = Config(
                        key=config_data["key"],
                        value=config_data["value"],
                        description=config_data.get("description", "")
                    )
                    db.session.add(new_config)
                imported_count += 1

        db.session.commit()
        return f"Import réussi: {imported_count} éléments importés"

    except Exception as e:
        db.session.rollback()
        raise e

if __name__ == "__main__":
    app.run(debug=True, port=5001)

