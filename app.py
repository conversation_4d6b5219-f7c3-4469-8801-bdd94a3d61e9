from flask import Flask, render_template, request, redirect, url_for, session
from flask_sqlalchemy import SQLAlchemy
import subprocess
import platform
import ipaddress
from datetime import datetime, timedelta
from pysnmp.hlapi import *
import concurrent.futures
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import json

app = Flask(__name__)
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///monitoring.db'
app.secret_key = "secret_key_super_secrète"  # Change-la en vraie clé secrète
db = SQLAlchemy(app)

# Host Groups (like Zabbix)
class HostGroup(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, unique=True)
    description = db.Column(db.String(500), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# Templates (like Zabbix)
class Template(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False, unique=True)
    description = db.Column(db.String(500), nullable=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)

# Enhanced Machine/Host model
class Machine(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    ip = db.Column(db.String(100), nullable=False)
    name = db.Column(db.String(200), nullable=False)
    status = db.Column(db.String(10), nullable=False)
    cpu_load = db.Column(db.Integer, nullable=True)
    memory_usage = db.Column(db.Integer, nullable=True)  # Memory usage percentage
    disk_usage = db.Column(db.Integer, nullable=True)    # Disk usage percentage
    uptime = db.Column(db.Integer, nullable=True)        # Uptime in seconds
    description = db.Column(db.String(500), nullable=True)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    host_group_id = db.Column(db.Integer, db.ForeignKey('host_group.id'), nullable=True)
    template_id = db.Column(db.Integer, db.ForeignKey('template.id'), nullable=True)
    snmp_community = db.Column(db.String(100), default='public')
    snmp_version = db.Column(db.String(10), default='2c')
    monitoring_enabled = db.Column(db.Boolean, default=True)

    # Relationships
    host_group = db.relationship('HostGroup', backref='machines')
    template = db.relationship('Template', backref='machines')

# Enhanced Alert model with severity levels
class Alert(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    ip = db.Column(db.String(100), nullable=False)
    name = db.Column(db.String(200), nullable=False)
    status = db.Column(db.String(10), nullable=False)
    message = db.Column(db.String(500), nullable=False)
    severity = db.Column(db.String(20), default='warning')  # info, warning, average, high, disaster
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)
    acknowledged = db.Column(db.Boolean, default=False)
    ack_timestamp = db.Column(db.DateTime, nullable=True)
    ack_user = db.Column(db.String(100), nullable=True)
    resolved = db.Column(db.Boolean, default=False)
    resolved_timestamp = db.Column(db.DateTime, nullable=True)

# Items (monitoring metrics like Zabbix)
class Item(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    key = db.Column(db.String(200), nullable=False)  # e.g., 'system.cpu.load', 'vm.memory.size'
    oid = db.Column(db.String(200), nullable=True)   # SNMP OID
    value_type = db.Column(db.String(20), default='numeric')  # numeric, text, log
    units = db.Column(db.String(50), nullable=True)  # %, B, bps, etc.
    description = db.Column(db.String(500), nullable=True)
    template_id = db.Column(db.Integer, db.ForeignKey('template.id'), nullable=True)
    active = db.Column(db.Boolean, default=True)

    # Relationship
    template = db.relationship('Template', backref='items')

# Item History (stores collected values)
class ItemHistory(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    item_id = db.Column(db.Integer, db.ForeignKey('item.id'), nullable=False)
    machine_id = db.Column(db.Integer, db.ForeignKey('machine.id'), nullable=False)
    value = db.Column(db.String(500), nullable=False)
    timestamp = db.Column(db.DateTime, default=datetime.utcnow)

    # Relationships
    item = db.relationship('Item', backref='history')
    machine = db.relationship('Machine', backref='item_history')

previous_status = {}

# Enhanced AlertRule (Triggers in Zabbix)
class AlertRule(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(200), nullable=False)
    metric = db.Column(db.String(100), nullable=False)
    operator = db.Column(db.String(10), nullable=False)
    value = db.Column(db.String(100), nullable=False)
    message = db.Column(db.String(500), nullable=False)
    severity = db.Column(db.String(20), default='warning')  # info, warning, average, high, disaster
    active = db.Column(db.Boolean, default=True)
    template_id = db.Column(db.Integer, db.ForeignKey('template.id'), nullable=True)
    host_group_id = db.Column(db.Integer, db.ForeignKey('host_group.id'), nullable=True)

    # Relationships
    template = db.relationship('Template', backref='alert_rules')
    host_group = db.relationship('HostGroup', backref='alert_rules')

# User management
class User(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(100), nullable=False, unique=True)
    email = db.Column(db.String(200), nullable=False, unique=True)
    password_hash = db.Column(db.String(200), nullable=False)
    role = db.Column(db.String(50), default='user')  # admin, user, viewer
    active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime, nullable=True)

# Configuration settings
class Config(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    key = db.Column(db.String(100), nullable=False, unique=True)
    value = db.Column(db.Text, nullable=False)
    description = db.Column(db.String(500), nullable=True)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow)

def ping(ip):
    param = "-n" if platform.system().lower() == "windows" else "-c"
    command = ["ping", param, "1", ip]
    result = subprocess.run(command, stdout=subprocess.DEVNULL)
    return result.returncode == 0

def snmp_get(ip, oid='*******.*******.0'):
    command = ['snmpget', '-v', '2c', '-c', 'public', '-t', '1', '-r', '1', ip, oid]
    try:
        result = subprocess.run(command, capture_output=True, text=True, timeout=2)
        if result.returncode != 0 or "No Response" in result.stdout or "Timeout" in result.stdout:
            return None
        
        parts = result.stdout.split(':', 3)
        if len(parts) > 1:
            value = parts[-1].strip()
            if value.startswith('"') and value.endswith('"'):
                return value[1:-1]
            return value
        return None
    except (subprocess.TimeoutExpired, FileNotFoundError):
        return None
    except Exception as e:
        print(f"[ERROR] Command-line snmpget failed for IP {ip} with OID {oid}: {e}")
        return None

def get_cpu_load(ip, description):
    if not description:
        return None

    desc_lower = description.lower()

    oid = None

    if 'linux' in desc_lower or 'x86_64' in desc_lower:
        oid = '*******.4.1.2021.11.11.0'  # idle CPU percentage
        idle_str = snmp_get(ip, oid)
        try:
            return 100 - int(idle_str)
        except (ValueError, TypeError):
            return None
    elif 'windows' in desc_lower:
        oid = '*******.********.3.1.2.196608'
    elif 'cisco' in desc_lower:
        oid = '*******.*******.*********.1.5'

    if oid:
        val = snmp_get(ip, oid)
        try:
            return int(val)
        except (ValueError, TypeError):
            return None

    return None

def get_memory_usage(ip, description):
    """Get memory usage percentage"""
    if not description:
        return None

    desc_lower = description.lower()

    if 'linux' in desc_lower or 'x86_64' in desc_lower:
        # Get total and available memory
        total_mem = snmp_get(ip, '*******.4.1.2021.4.5.0')  # memTotalReal
        avail_mem = snmp_get(ip, '*******.4.1.2021.4.6.0')  # memAvailReal
        try:
            total = int(total_mem)
            avail = int(avail_mem)
            used = total - avail
            return int((used / total) * 100)
        except (ValueError, TypeError):
            return None
    elif 'windows' in desc_lower:
        # Windows memory usage
        total_mem = snmp_get(ip, '*******.********.2.0')  # hrMemorySize
        try:
            # This is a simplified approach for Windows
            return None  # Would need more complex SNMP walking
        except (ValueError, TypeError):
            return None

    return None

def get_disk_usage(ip, description):
    """Get disk usage percentage for root partition"""
    if not description:
        return None

    desc_lower = description.lower()

    if 'linux' in desc_lower or 'x86_64' in desc_lower:
        # Get disk usage for root partition
        disk_total = snmp_get(ip, '*******.4.1.2021.*******')  # dskTotal
        disk_used = snmp_get(ip, '*******.4.1.2021.*******')   # dskUsed
        try:
            total = int(disk_total)
            used = int(disk_used)
            return int((used / total) * 100)
        except (ValueError, TypeError):
            return None

    return None

def get_uptime(ip):
    """Get system uptime in seconds"""
    uptime_str = snmp_get(ip, '*******.*******.0')  # sysUpTime
    try:
        # sysUpTime is in hundredths of a second
        return int(uptime_str) // 100
    except (ValueError, TypeError):
        return None

def scan_ip(ip_str):
    is_up = ping(ip_str)
    name = None
    cpu_load = None
    memory_usage = None
    disk_usage = None
    uptime = None
    description = None

    if is_up:
        description = snmp_get(ip_str, oid='*******.*******.0')
        name = snmp_get(ip_str)  # sysName

        if description:
            desc_lower = description.lower()

            NON_SERVER_KEYWORDS = ['printer', 'imprimante', 'epson', 'hp', 'canon', 'lexmark', 'xerox', 'scanner', 'plotter']
            SERVER_KEYWORDS = ['server', 'ubuntu', 'debian', 'centos', 'red hat', 'windows', 'linux', 'srv']

            if any(non_kw in desc_lower for non_kw in NON_SERVER_KEYWORDS):
                print(f"[INFO] {ip_str} ignoré (imprimante ou périphérique - description : {description})")
                return None

            if not any(srv_kw in desc_lower for srv_kw in SERVER_KEYWORDS):
                print(f"[INFO] {ip_str} ignoré (pas identifié comme un serveur - description : {description})")
                return None

            # Collect all metrics
            cpu_load = get_cpu_load(ip_str, description)
            memory_usage = get_memory_usage(ip_str, description)
            disk_usage = get_disk_usage(ip_str, description)
            uptime = get_uptime(ip_str)

        if not description and not name:
            print(f"[INFO] Pas de réponse SNMP de {ip_str}. Appareil hors ligne ou SNMP non configuré.")

    return {
        "ip": ip_str,
        "status": "up" if is_up else "down",
        "name": name if name else "N/A",
        "cpu_load": cpu_load,
        "memory_usage": memory_usage,
        "disk_usage": disk_usage,
        "uptime": uptime,
        "description": description if description else "N/A"
    }

def scan_network(network_cidr):
    network = ipaddress.ip_network(network_cidr, strict=False)
    ips = [str(ip) for ip in network.hosts()]
    with concurrent.futures.ThreadPoolExecutor(max_workers=50) as executor:
        results = executor.map(scan_ip, ips)
    return list(results)

# SMTP config (à adapter)
SMTP_SERVER = "smtp.gmail.com"
SMTP_PORT = 587
SMTP_USER = "<EMAIL>"        
SMTP_PASSWORD = "mxxq xxwx ejqi yfqj"          

EMAIL_FROM = SMTP_USER
EMAIL_TO = "<EMAIL>"       

def send_email(subject, message):
    msg = MIMEMultipart()
    msg['From'] = EMAIL_FROM
    msg['To'] = EMAIL_TO
    msg['Subject'] = subject
    msg.attach(MIMEText(message, 'plain'))
    try:
        server = smtplib.SMTP(SMTP_SERVER, SMTP_PORT)
        server.starttls()
        server.login(SMTP_USER, SMTP_PASSWORD)
        server.sendmail(EMAIL_FROM, EMAIL_TO, msg.as_string())
        server.quit()
        print(f"Email envoyé : {subject}")
    except Exception as e:
        print(f"Erreur envoi email : {e}")

@app.route("/", methods=["GET", "POST"])
def index():
    alerts = []
    error = None
    global previous_status

    if request.method == "POST":
        network_cidr = request.form.get("network_cidr")
        try:
            ipaddress.ip_network(network_cidr, strict=False)
            scanned = scan_network(network_cidr)
            scan_timestamp = datetime.utcnow()

            for machine in scanned:
                if machine is None:
                    continue  # Ignore non-servers

                entry = Machine(
                    ip=machine["ip"],
                    name=machine["name"],
                    status=machine["status"],
                    cpu_load=machine["cpu_load"],
                    memory_usage=machine["memory_usage"],
                    disk_usage=machine["disk_usage"],
                    uptime=machine["uptime"],
                    timestamp=scan_timestamp,
                    description=machine["description"]
                )
                db.session.add(entry)
            db.session.commit()

            session['last_scan_time'] = scan_timestamp.isoformat()
            session['last_ip_searched'] = network_cidr

            # Gestion alertes changement d'état simple
            current_status = {m["ip"]: m["status"] for m in scanned if m is not None}
            for machine in scanned:
                if machine is None:
                    continue
                ip = machine["ip"]
                curr = machine["status"]
                prev = previous_status.get(ip)

                if prev and prev != curr:
                    if curr == "down":
                        alert_msg = f"⚠️ {ip} ({machine['name']}) est maintenant HORS LIGNE."
                        severity = "high"
                    else:
                        alert_msg = f"✅ {ip} ({machine['name']}) est de nouveau EN LIGNE."
                        severity = "info"

                    last_alert = Alert.query.filter_by(ip=ip, status=curr, acknowledged=False).order_by(Alert.timestamp.desc()).first()
                    if not last_alert or (datetime.utcnow() - last_alert.timestamp).total_seconds() > 300:
                        new_alert = Alert(ip=ip, name=machine['name'], status=curr, message=alert_msg, severity=severity)
                        db.session.add(new_alert)
                        db.session.commit()
                        send_email(
                            f"[{severity.upper()}] Alerte Monitoring - Machine " + ("hors ligne" if curr == "down" else "en ligne"),
                            alert_msg
                        )
                        alerts.append(alert_msg)

            previous_status = current_status

            # Appliquer règles personnalisées
            from jinja2 import Template
            rules = AlertRule.query.filter_by(active=True).all()

            for machine in scanned:
                if machine is None:
                    continue
                context = {
                    "ip": machine["ip"],
                    "status": machine["status"],
                    "name": machine["name"],
                    "cpu_load": machine["cpu_load"],
                    "memory_usage": machine["memory_usage"],
                    "disk_usage": machine["disk_usage"],
                    "uptime": machine["uptime"],
                    "description": machine["description"]
                }
                for rule in rules:
                    metric_value = context.get(rule.metric)
                    if metric_value is None:
                        continue

                    match = False
                    is_numeric_comparison = rule.operator in ('>', '<', '>=', '<=')

                    if is_numeric_comparison:
                        try:
                            metric_val_num = float(metric_value)
                            rule_val_num = float(rule.value)
                            if rule.operator == '>' and metric_val_num > rule_val_num: match = True
                            elif rule.operator == '<' and metric_val_num < rule_val_num: match = True
                            elif rule.operator == '>=' and metric_val_num >= rule_val_num: match = True
                            elif rule.operator == '<=' and metric_val_num <= rule_val_num: match = True
                        except (ValueError, TypeError):
                            pass
                    else:
                        if rule.operator == '==' and str(metric_value) == rule.value:
                            match = True
                        elif rule.operator == '!=' and str(metric_value) != rule.value:
                            match = True

                    if match:
                        template = Template(rule.message)
                        alert_msg = template.render(**context)

                        last_alert = Alert.query.filter_by(ip=machine["ip"], message=alert_msg, acknowledged=False)\
                                                 .order_by(Alert.timestamp.desc()).first()

                        if not last_alert or (datetime.utcnow() - last_alert.timestamp).total_seconds() > 300:
                            new_alert = Alert(ip=machine["ip"], name=machine["name"], status=machine["status"],
                                            message=alert_msg, severity=rule.severity)
                            db.session.add(new_alert)
                            db.session.commit()

                            alerts.append(alert_msg)
                            send_email(f"[{rule.severity.upper()}] Alerte Monitoring: {rule.name}", alert_msg)

            return render_template("index.html", results=scanned, alerts=alerts, error=error, ip_input=network_cidr)

        except ValueError:
            error = "Format invalide. Exemple attendu : ***********/24"

    results = []
    ip_input = ""
    if 'last_scan_time' in session:
        scan_time = datetime.fromisoformat(session['last_scan_time'])
        time_start = scan_time - timedelta(seconds=10)
        time_end = scan_time + timedelta(seconds=10)
        results = Machine.query.filter(Machine.timestamp.between(time_start, time_end)).all()

    if 'last_ip_searched' in session:
        ip_input = session['last_ip_searched']

    return render_template("index.html", results=results, alerts=alerts, error=error, ip_input=ip_input)

@app.route("/historique")
def historique():
    machines = Machine.query.order_by(Machine.timestamp.desc()).limit(250).all()
    return render_template("historique.html", machines=machines)
@app.route("/machine/<ip>")
def machine_detail(ip):
    history = Machine.query.filter(Machine.ip == ip).order_by(Machine.timestamp.asc()).all()

    last_entry = Machine.query.filter_by(ip=ip).order_by(Machine.timestamp.desc()).first()
    machine_name = last_entry.name if last_entry else "N/A"
    machine_description = last_entry.description if last_entry else "N/A"

    chart_labels = [h.timestamp.strftime('%Y-%m-%d %H:%M:%S') for h in history]
    chart_cpu_data = [h.cpu_load if h.cpu_load is not None else 0 for h in history]
    chart_memory_data = [h.memory_usage if h.memory_usage is not None else 0 for h in history]
    chart_disk_data = [h.disk_usage if h.disk_usage is not None else 0 for h in history]

    return render_template("machine_detail.html",
                           machine_ip=ip,
                           machine_name=machine_name,
                           machine_description=machine_description,
                           history=history,
                           chart_labels=chart_labels,
                           chart_cpu_data=chart_cpu_data,
                           chart_memory_data=chart_memory_data,
                           chart_disk_data=chart_disk_data)


@app.route("/alerts")
def alerts():
    alerts = Alert.query.order_by(Alert.timestamp.desc()).limit(100).all()
    return render_template("alerts.html", alerts=alerts)

@app.route("/alert/acknowledge/<int:alert_id>", methods=["POST"])
def acknowledge_alert(alert_id):
    alert = Alert.query.get_or_404(alert_id)
    alert.acknowledged = True
    alert.ack_timestamp = datetime.utcnow()
    alert.ack_user = "admin"
    db.session.commit()
    return redirect(url_for('alerts'))

from sqlalchemy import func

@app.route("/dashboard")
def dashboard():
    now = datetime.utcnow()
    start_time = now - timedelta(hours=24)

    results = db.session.query(
        func.strftime('%Y-%m-%d %H:00:00', Machine.timestamp).label('hour'),
        func.avg(Machine.cpu_load).label('avg_cpu')
    ).filter(
        Machine.timestamp >= start_time,
        Machine.cpu_load != None
    ).group_by('hour').order_by('hour').all()

    cpu_labels = [r.hour for r in results]
    cpu_data = [round(r.avg_cpu, 2) for r in results]

    total_machines = Machine.query.count()
    up_count = Machine.query.filter_by(status="up").count()
    down_count = total_machines - up_count
    alert_count = Alert.query.filter(Alert.timestamp >= now.replace(hour=0, minute=0, second=0)).count()

    # Get top CPU usage hosts
    top_cpu = Machine.query.filter(Machine.cpu_load.isnot(None)).order_by(Machine.cpu_load.desc()).limit(10).all()

    # Get unacknowledged alerts
    unack_alerts = Alert.query.filter_by(acknowledged=False).order_by(Alert.timestamp.desc()).limit(10).all()

    return render_template("dashboard.html",
                           total_machines=total_machines,
                           up_count=up_count,
                           down_count=down_count,
                           alert_count=alert_count,
                           cpu_labels=json.dumps(cpu_labels),
                           cpu_data=json.dumps(cpu_data),
                           top_cpu=top_cpu,
                           alerts=unack_alerts)


@app.route("/rules", methods=["GET", "POST"])
def manage_rules():
    if request.method == "POST":
        name = request.form.get("name")
        metric = request.form.get("metric")
        operator = request.form.get("operator")
        value = request.form.get("value")
        message = request.form.get("message")
        severity = request.form.get("severity", "warning")
        new_rule = AlertRule(name=name, metric=metric, operator=operator, value=value, message=message, severity=severity)
        db.session.add(new_rule)
        db.session.commit()
        return redirect(url_for("manage_rules"))

    rules = AlertRule.query.all()
    return render_template("rules.html", rules=rules)

@app.route("/rules/delete/<int:rule_id>", methods=["POST"])
def delete_rule(rule_id):
    rule = AlertRule.query.get_or_404(rule_id)
    db.session.delete(rule)
    db.session.commit()
    return redirect(url_for("manage_rules"))

@app.route("/rules/toggle/<int:rule_id>", methods=["POST"])
def toggle_rule(rule_id):
    rule = AlertRule.query.get_or_404(rule_id)
    rule.active = not rule.active
    db.session.commit()
    return redirect(url_for("manage_rules"))

@app.route("/rules/edit/<int:rule_id>", methods=["GET", "POST"])
def edit_rule(rule_id):
    rule = AlertRule.query.get_or_404(rule_id)
    if request.method == "POST":
        rule.name = request.form.get("name")
        rule.metric = request.form.get("metric")
        rule.operator = request.form.get("operator")
        rule.value = request.form.get("value")
        rule.message = request.form.get("message")
        rule.severity = request.form.get("severity", "warning")
        db.session.commit()
        return redirect(url_for("manage_rules"))
    return render_template("edit_rule.html", rule=rule)


if __name__ == "__main__":
    app.run(debug=True)

