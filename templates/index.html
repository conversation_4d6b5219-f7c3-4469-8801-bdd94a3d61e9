{% extends "base.html" %}

{% block head %}
  {{ super() }}
  {# Ici aucune balise favicon #}
{% endblock %}

{% block content %}
<div class="container py-4">
  <h1 class="mb-4">🖥️ Monitoring R<PERSON><PERSON></h1>

  <form method="post" class="mb-4 d-flex flex-wrap align-items-end gap-3">
    <div class="flex-grow-1">
      <label for="network_cidr" class="form-label">Plage IP à scanner</label>
      <input type="text" class="form-control" id="network_cidr" name="network_cidr" placeholder="Ex : ***********/24" value="{{ ip_input }}" required />
    </div>
    <button type="submit" class="btn btn-primary mt-3 mt-md-0">🔍 Lancer le scan</button>
    <a href="{{ url_for('historique') }}" class="btn btn-outline-secondary mt-3 mt-md-0">📜 Voir l'historique</a>
  </form>

  {% if error %}
  <div class="alert alert-danger">{{ error }}</div>
  {% endif %}

  {% if alerts %}
  <div class="alert alert-warning">
    <h5>⚠️ Alertes :</h5>
    <ul>
      {% for alert in alerts %}
      <li>{{ alert }}</li>
      {% endfor %}
    </ul>
  </div>
  {% endif %}

  {% if results %}
  <div class="d-flex justify-content-between align-items-center mb-2">
    <div>
      <button id="filter-all" data-status="all" class="btn btn-outline-primary btn-sm btn-filter active">Tous</button>
      <button id="filter-up" data-status="up" class="btn btn-outline-success btn-sm btn-filter">En ligne</button>
      <button id="filter-down" data-status="down" class="btn btn-outline-danger btn-sm btn-filter">Hors ligne</button>
    </div>
    <div>
      <label>Afficher
        <select id="rowsCount" class="form-select form-select-sm d-inline-block w-auto ms-2">
          <option>5</option>
          <option selected>10</option>
          <option>25</option>
          <option>50</option>
          <option>100</option>
        </select>
        lignes
      </label>
    </div>
  </div>

  <table id="resultsTable" class="table table-bordered table-striped table-hover">
    <thead class="table-dark">
      <tr>
        <th>Adresse IP</th>
        <th>Nom (SNMP)</th>
        <th>État</th>
      </tr>
    </thead>
    <tbody>
      {% for machine in results %}
      <tr data-status="{{ machine['status'] }}">
        <td><a href="{{ url_for('machine_detail', ip=machine['ip']) }}">{{ machine['ip'] }}</a></td>
        <td>{{ machine['name'] }}</td>
        <td>
          {% if machine['status'] == "up" %}
          <span class="badge bg-success">En ligne</span>
          {% else %}
          <span class="badge bg-danger">Hors ligne</span>
          {% endif %}
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
  {% endif %}
</div>

<!-- CSS et JS DataTables + jQuery -->
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet" />
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script>
  $(document).ready(function() {
    var table = $('#resultsTable').DataTable({
      language: {
        url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
      },
      pageLength: 10,
      lengthChange: false,
      dom: 'lrtip'  // Pas de recherche native, on fait filtres custom
    });

    $('#rowsCount').on('change', function() {
      table.page.len($(this).val()).draw();
    });

    // Filtre personnalisé DataTables selon le bouton actif
    $.fn.dataTable.ext.search.push(
      function(settings, data, dataIndex) {
        var selectedStatus = $('.btn-filter.active').data('status');
        var status = $('#resultsTable tbody tr').eq(dataIndex).data('status');
        if (selectedStatus === 'all') {
          return true; // Tout afficher
        }
        return status === selectedStatus;
      }
    );

    function updateFilter(btn) {
      $('.btn-filter').removeClass('active');
      $(btn).addClass('active');
      table.draw();
    }

    $('#filter-all').on('click', function() {
      updateFilter(this);
    });
    $('#filter-up').on('click', function() {
      updateFilter(this);
    });
    $('#filter-down').on('click', function() {
      updateFilter(this);
    });
  });
</script>
{% endblock %}
