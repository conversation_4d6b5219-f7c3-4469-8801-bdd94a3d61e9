{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2>⚙️ Gestion des règles d'alerte</h2>
<a href="{{ url_for('index') }}" class="btn btn-secondary mb-3">← Accueil</a>

  <!-- Formulaire d'ajout -->
  <div class="card mb-4">
    <div class="card-body">
      <h5 class="card-title">➕ Nouvelle règle</h5>
      <form method="POST">
        <div class="row mb-3">
          <div class="col-md-4">
            <label for="name" class="form-label">Nom</label>
            <input type="text" class="form-control" name="name" required>
          </div>
          <div class="col-md-2">
            <label for="metric" class="form-label">Métrique</label>
            <select class="form-select" name="metric" required>
              <option value="status">status</option>
              <option value="cpu_load">cpu_load</option>
              <option value="memory_usage">memory_usage</option>
              <option value="disk_usage">disk_usage</option>
              <option value="uptime">uptime</option>
            </select>
          </div>
          <div class="col-md-2">
            <label for="operator" class="form-label">Opérateur</label>
            <select class="form-select" name="operator" required>
              <option value="==">==</option>
              <option value="!=">!=</option>
              <option value=">">></option>
              <option value="<"><</option>
              <option value=">=">>=</option>
              <option value="<="><=</option>
            </select>
          </div>
          <div class="col-md-2">
            <label for="value" class="form-label">Valeur</label>
            <input type="text" class="form-control" name="value" required>
          </div>
          <div class="col-md-2">
            <label for="severity" class="form-label">Sévérité</label>
            <select class="form-select" name="severity" required>
              <option value="info">Info</option>
              <option value="warning" selected>Warning</option>
              <option value="average">Average</option>
              <option value="high">High</option>
              <option value="disaster">Disaster</option>
            </select>
          </div>
        </div>
        <div class="mb-3">
          <label for="message" class="form-label">Message d’alerte (tu peux utiliser {{ ip }}, {{ name }}, {{ cpu_load }})</label>
          <input type="text" class="form-control" name="message" required>
        </div>
        <button type="submit" class="btn btn-primary">💾 Enregistrer</button>
      </form>
    </div>
  </div>

  <!-- Liste des règles -->
  <h3>📋 Règles existantes</h3>
  <table class="table table-bordered">
    <thead class="table-dark">
      <tr>
        <th>Nom</th>
        <th>Métrique</th>
        <th>Condition</th>
        <th>Sévérité</th>
        <th>Message</th>
        <th>Active</th>
        <th>Actions</th>
      </tr>
    </thead>
    <tbody>
      {% for rule in rules %}
      <tr>
        <td>{{ rule.name }}</td>
        <td>{{ rule.metric }}</td>
        <td>{{ rule.operator }} {{ rule.value }}</td>
        <td>
          {% if rule.severity == 'disaster' %}
            <span class="badge bg-danger">🔥 DISASTER</span>
          {% elif rule.severity == 'high' %}
            <span class="badge bg-warning text-dark">⚠️ HIGH</span>
          {% elif rule.severity == 'average' %}
            <span class="badge bg-info">📊 AVERAGE</span>
          {% elif rule.severity == 'warning' %}
            <span class="badge bg-secondary">⚡ WARNING</span>
          {% else %}
            <span class="badge bg-light text-dark">ℹ️ INFO</span>
          {% endif %}
        </td>
        <td>{{ rule.message }}</td>
        <td>
          {% if rule.active %}
            <span class="badge bg-success">Oui</span>
          {% else %}
            <span class="badge bg-secondary">Non</span>
          {% endif %}
        </td>
        <td>
          <div class="d-flex gap-1">
            <a href="{{ url_for('edit_rule', rule_id=rule.id) }}" class="btn btn-sm btn-primary">✏️ Modifier</a>
            <form action="{{ url_for('toggle_rule', rule_id=rule.id) }}" method="post">
              <button class="btn btn-sm btn-warning" type="submit">
                {% if rule.active %}Désactiver{% else %}Activer{% endif %}
              </button>
            </form>
            <form action="{{ url_for('delete_rule', rule_id=rule.id) }}" method="post" onsubmit="return confirm('Supprimer cette règle ?');">
              <button class="btn btn-sm btn-danger" type="submit">🗑 Supprimer</button>
            </form>
          </div>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
{% endblock %}
