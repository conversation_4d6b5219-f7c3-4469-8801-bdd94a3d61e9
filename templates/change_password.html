{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2><i class="bi bi-key"></i> Changer de mot de passe</h2>
  <a href="{{ url_for('profile') }}" class="btn btn-secondary mb-3">← Retour au profil</a>

  <!-- Messages Flash -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          {{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <div class="row justify-content-center">
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="bi bi-shield-lock"></i> Modification du mot de passe</h5>
        </div>
        <div class="card-body">
          <form method="POST">
            <div class="mb-3">
              <label for="current_password" class="form-label">Mot de passe actuel</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-lock"></i></span>
                <input type="password" class="form-control" id="current_password" name="current_password" required>
              </div>
            </div>
            
            <div class="mb-3">
              <label for="new_password" class="form-label">Nouveau mot de passe</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-key"></i></span>
                <input type="password" class="form-control" id="new_password" name="new_password" required>
              </div>
              <div class="form-text">Le mot de passe doit contenir au moins 6 caractères.</div>
            </div>
            
            <div class="mb-3">
              <label for="confirm_password" class="form-label">Confirmer le nouveau mot de passe</label>
              <div class="input-group">
                <span class="input-group-text"><i class="bi bi-key-fill"></i></span>
                <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
              </div>
            </div>
            
            <div class="d-grid gap-2">
              <button type="submit" class="btn btn-primary">
                <i class="bi bi-check-circle"></i> Changer le mot de passe
              </button>
              <a href="{{ url_for('profile') }}" class="btn btn-outline-secondary">
                <i class="bi bi-x-circle"></i> Annuler
              </a>
            </div>
          </form>
        </div>
        <div class="card-footer">
          <div class="alert alert-info mb-0">
            <i class="bi bi-info-circle"></i> Conseils de sécurité :
            <ul class="mb-0">
              <li>Utilisez un mot de passe d'au moins 8 caractères</li>
              <li>Incluez des lettres majuscules et minuscules</li>
              <li>Ajoutez des chiffres et des caractères spéciaux</li>
              <li>Évitez les mots du dictionnaire et informations personnelles</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
