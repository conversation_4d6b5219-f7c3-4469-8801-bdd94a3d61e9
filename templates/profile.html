{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2><i class="bi bi-person-circle"></i> Profil Utilisateur</h2>
  <a href="{{ url_for('index') }}" class="btn btn-secondary mb-3">← Accueil</a>

  <!-- Messages Flash -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          {{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <div class="row">
    <!-- Informations utilisateur -->
    <div class="col-md-6">
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="bi bi-person-badge"></i> Informations du compte</h5>
        </div>
        <div class="card-body">
          <div class="d-flex align-items-center mb-4">
            <div class="bg-light rounded-circle p-3 me-3">
              <i class="bi bi-person-fill fs-1 text-primary"></i>
            </div>
            <div>
              <h4>{{ user.username }}</h4>
              <span class="badge {% if user.role == 'admin' %}bg-danger{% elif user.role == 'user' %}bg-success{% else %}bg-info{% endif %}">
                {{ user.role|capitalize }}
              </span>
              <span class="badge {% if user.active %}bg-success{% else %}bg-secondary{% endif %}">
                {% if user.active %}Actif{% else %}Inactif{% endif %}
              </span>
            </div>
          </div>

          <ul class="list-group list-group-flush">
            <li class="list-group-item d-flex justify-content-between align-items-center">
              <span><i class="bi bi-envelope"></i> Email</span>
              <span class="text-muted">{{ user.email }}</span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              <span><i class="bi bi-calendar-check"></i> Compte créé le</span>
              <span class="text-muted">{{ user.created_at.strftime('%d/%m/%Y à %H:%M') }}</span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              <span><i class="bi bi-clock-history"></i> Dernière connexion</span>
              <span class="text-muted">{{ user.last_login.strftime('%d/%m/%Y à %H:%M') if user.last_login else 'Jamais' }}</span>
            </li>
          </ul>
        </div>
        <div class="card-footer">
          <a href="{{ url_for('change_password') }}" class="btn btn-outline-primary">
            <i class="bi bi-key"></i> Changer de mot de passe
          </a>
        </div>
      </div>

      <!-- Permissions -->
      <div class="card shadow-sm mb-4">
        <div class="card-header bg-info text-white">
          <h5 class="mb-0"><i class="bi bi-shield-lock"></i> Permissions</h5>
        </div>
        <div class="card-body">
          <ul class="list-group">
            <li class="list-group-item d-flex justify-content-between align-items-center">
              Lecture (dashboards, alertes)
              <span class="badge bg-success rounded-pill">
                <i class="bi bi-check-lg"></i>
              </span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              Écriture (acquitter alertes, scan)
              <span class="badge {% if user.has_permission('write') %}bg-success{% else %}bg-danger{% endif %} rounded-pill">
                {% if user.has_permission('write') %}<i class="bi bi-check-lg"></i>{% else %}<i class="bi bi-x-lg"></i>{% endif %}
              </span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              Suppression (règles, groupes)
              <span class="badge {% if user.has_permission('delete') %}bg-success{% else %}bg-danger{% endif %} rounded-pill">
                {% if user.has_permission('delete') %}<i class="bi bi-check-lg"></i>{% else %}<i class="bi bi-x-lg"></i>{% endif %}
              </span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              Configuration (email, système)
              <span class="badge {% if user.has_permission('config') %}bg-success{% else %}bg-danger{% endif %} rounded-pill">
                {% if user.has_permission('config') %}<i class="bi bi-check-lg"></i>{% else %}<i class="bi bi-x-lg"></i>{% endif %}
              </span>
            </li>
            <li class="list-group-item d-flex justify-content-between align-items-center">
              Gestion utilisateurs
              <span class="badge {% if user.has_permission('user_management') %}bg-success{% else %}bg-danger{% endif %} rounded-pill">
                {% if user.has_permission('user_management') %}<i class="bi bi-check-lg"></i>{% else %}<i class="bi bi-x-lg"></i>{% endif %}
              </span>
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Historique de connexion -->
    <div class="col-md-6">
      <div class="card shadow-sm">
        <div class="card-header bg-secondary text-white">
          <h5 class="mb-0"><i class="bi bi-clock-history"></i> Historique de connexion</h5>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-striped table-hover">
              <thead>
                <tr>
                  <th>Date</th>
                  <th>IP</th>
                  <th>Navigateur</th>
                  <th>Durée</th>
                </tr>
              </thead>
              <tbody>
                {% for session in sessions %}
                <tr>
                  <td>{{ session.login_time.strftime('%d/%m/%Y %H:%M') }}</td>
                  <td>{{ session.ip_address }}</td>
                  <td>
                    <small class="text-muted">
                      {{ session.user_agent.split('(')[0] if session.user_agent else 'Inconnu' }}
                    </small>
                  </td>
                  <td>
                    {% if session.logout_time %}
                      {% set duration = (session.logout_time - session.login_time).total_seconds() // 60 %}
                      {{ duration }} min
                    {% elif loop.first %}
                      <span class="badge bg-success">En cours</span>
                    {% else %}
                      <span class="badge bg-warning text-dark">Non terminée</span>
                    {% endif %}
                  </td>
                </tr>
                {% else %}
                <tr>
                  <td colspan="4" class="text-center">Aucun historique de connexion</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
