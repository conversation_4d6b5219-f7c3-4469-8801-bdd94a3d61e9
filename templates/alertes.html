<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <title>Alertes</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
</head>
<body class="bg-light">
  <div class="container py-4">
    <h2>📢 Historique des Alertes</h2>
    <a href="/" class="btn btn-secondary mb-3">← Retour</a>

    <table class="table table-bordered table-striped">
      <thead class="table-dark">
        <tr>
          <th>IP</th>
          <th>Nom</th>
          <th>Statut</th>
          <th>Message</th>
          <th>Date</th>
          <th>Pris en compte</th>
          <th>Action</th>
        </tr>
      </thead>
      <tbody>
        {% for alert in alerts %}
        <tr>
          <td>{{ alert.ip }}</td>
          <td>{{ alert.name }}</td>
          <td>{{ alert.status }}</td>
          <td>{{ alert.message }}</td>
          <td>{{ alert.timestamp.strftime('%d/%m/%Y %H:%M:%S') }}</td>
          <td>
            {% if alert.acknowledged %}
              Oui ({{ alert.ack_timestamp.strftime('%d/%m/%Y %H:%M:%S') }})
            {% else %}
              Non
            {% endif %}
          </td>
          <td>
            {% if not alert.acknowledged %}
              <form action="{{ url_for('acknowledge_alert', alert_id=alert.id) }}" method="post">
                <button class="btn btn-sm btn-success" type="submit">Acknowledge</button>
              </form>
            {% else %}
              <button class="btn btn-sm btn-secondary" disabled>Pris en compte</button>
            {% endif %}
          </td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</body>
</html>
