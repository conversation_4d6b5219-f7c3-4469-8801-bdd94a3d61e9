<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1" />
  <title>Monitoring <PERSON><PERSON><PERSON></title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet" />
  <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css" />
  <style>
    * {
      box-sizing: border-box;
    }

    body {
      margin: 0;
      padding: 0;
      height: 100vh;
      overflow-x: hidden;
      display: flex;
    }

    #sidebar {
      width: 250px;
      background-color: #212529;
      color: white;
      position: fixed;
      top: 0;
      left: 0;
      height: 100vh;
      padding: 20px;
      overflow-y: auto;
      z-index: 1000;
      transition: width 0.3s ease;
    }

    #sidebar.collapsed {
      width: 70px;
      padding: 20px 10px;
    }

    /* <PERSON>acher les textes quand sidebar est réduite */
    #sidebar.collapsed a span,
    #sidebar.collapsed .fs-4 {
      display: none;
    }

    #sidebar .nav-link {
      color: #adb5bd;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      transition: background-color 0.2s;
    }

    #sidebar .nav-link i {
      margin-right: 10px;
      font-size: 1.2rem;
      min-width: 24px;
      text-align: center;
    }

    #sidebar .nav-link.active,
    #sidebar .nav-link:hover {
      background-color: #343a40;
      color: white;
      border-radius: 5px;
    }

    #content {
      margin-left: 250px;
      padding: 20px;
      background-color: #f8f9fa;
      flex-grow: 1;
      min-height: 100vh;
      transition: margin-left 0.3s ease;
    }

    /* Décaler contenu quand sidebar réduite */
    #sidebar.collapsed ~ #content {
      margin-left: 70px;
    }

    .fs-4 {
      font-weight: bold;
    }

    /* Bouton toggle */
    #toggleSidebarBtn {
      position: fixed;
      top: 15px;
      left: 260px;
      z-index: 1100;
      background-color: #343a40;
      border: none;
      color: white;
      padding: 5px 10px;
      font-size: 1.2rem;
      border-radius: 4px;
      cursor: pointer;
      transition: left 0.3s ease;
    }

    #sidebar.collapsed ~ #toggleSidebarBtn {
      left: 80px;
    }

    @media (max-width: 768px) {
      #sidebar {
        position: relative;
        width: 100%;
        height: auto;
      }

      #sidebar.collapsed {
        width: 100%;
        padding: 20px;
      }

      #content {
        margin-left: 0;
      }

      #toggleSidebarBtn {
        display: none;
      }
    }
  </style>
</head>
<body>

  <nav id="sidebar">
    <a href="/" class="d-flex align-items-center mb-4 text-white text-decoration-none">
      <span class="fs-4"><i class="bi bi-graph-up-arrow me-2"></i>Monitoring</span>
    </a>
    <hr class="text-secondary" />
    <ul class="nav nav-pills flex-column mb-auto">
      <li class="nav-item">
        <a href="{{ url_for('index') }}" class="nav-link {% if request.path == '/' %}active{% endif %}">
          <i class="bi bi-house-door-fill"></i> <span>Accueil</span>
        </a>
      </li>
      <li>
        <a href="{{ url_for('historique') }}" class="nav-link {% if request.path == '/historique' %}active{% endif %}">
          <i class="bi bi-clock-history"></i> <span>Historique</span>
        </a>
      </li>
      <li>
        <a href="{{ url_for('alerts') }}" class="nav-link {% if request.path.startswith('/alerts') %}active{% endif %}">
          <i class="bi bi-bell-fill"></i> <span>Alertes</span>
        </a>
      </li>
      <li>
        <a href="{{ url_for('dashboard') }}" class="nav-link {% if request.path == '/dashboard' %}active{% endif %}">
          <i class="bi bi-speedometer2"></i> <span>Dashboard</span>
        </a>
      </li>
      <li>
        <a href="{{ url_for('manage_rules') }}" class="nav-link {% if request.path.startswith('/rules') %}active{% endif %}">
          <i class="bi bi-file-earmark-ruled-fill"></i> <span>Règles</span>
        </a>
      </li>
      <li>
        <a href="{{ url_for('manage_hosts') }}" class="nav-link {% if request.path.startswith('/hosts') %}active{% endif %}">
          <i class="bi bi-hdd-network-fill"></i> <span>Hôtes</span>
        </a>
      </li>
      <li>
        <a href="{{ url_for('manage_hostgroups') }}" class="nav-link {% if request.path.startswith('/hostgroups') %}active{% endif %}">
          <i class="bi bi-collection-fill"></i> <span>Groupes</span>
        </a>
      </li>
      <li>
        <a href="{{ url_for('manage_templates') }}" class="nav-link {% if request.path.startswith('/templates') %}active{% endif %}">
          <i class="bi bi-file-earmark-code-fill"></i> <span>Templates</span>
        </a>
      </li>
    </ul>
    <hr class="text-secondary" />
    <ul class="nav nav-pills flex-column mb-auto">
      <li>
        <a href="{{ url_for('manage_config') }}" class="nav-link {% if request.path.startswith('/config') %}active{% endif %}">
          <i class="bi bi-gear-fill"></i> <span>Configuration</span>
        </a>
      </li>
      <li>
        <a href="{{ url_for('housekeeping') }}" class="nav-link {% if request.path.startswith('/housekeeping') %}active{% endif %}">
          <i class="bi bi-trash3-fill"></i> <span>Maintenance</span>
        </a>
      </li>
      <li>
        <a href="{{ url_for('export_import') }}" class="nav-link {% if request.path.startswith('/export-import') %}active{% endif %}">
          <i class="bi bi-box-arrow-in-down-left"></i> <span>Export/Import</span>
        </a>
      </li>
      {% if current_user.is_authenticated and current_user.is_admin() %}
      <li>
        <a href="{{ url_for('manage_users') }}" class="nav-link {% if request.path.startswith('/users') %}active{% endif %}">
          <i class="bi bi-people-fill"></i> <span>Utilisateurs</span>
        </a>
      </li>
      {% endif %}
    </ul>
    <hr class="text-secondary" />

    <!-- Section utilisateur -->
    {% if current_user.is_authenticated %}
    <div class="dropdown">
      <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
        <div class="bg-light rounded-circle p-2 me-2">
          <i class="bi bi-person-fill text-primary"></i>
        </div>
        <div>
          <strong>{{ current_user.username }}</strong>
          <br><small class="text-muted">{{ current_user.role|capitalize }}</small>
        </div>
      </a>
      <ul class="dropdown-menu dropdown-menu-dark text-small shadow">
        <li><a class="dropdown-item" href="{{ url_for('profile') }}"><i class="bi bi-person-circle"></i> Mon profil</a></li>
        <li><a class="dropdown-item" href="{{ url_for('change_password') }}"><i class="bi bi-key"></i> Changer mot de passe</a></li>
        {% if current_user.is_admin() %}
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{{ url_for('manage_users') }}"><i class="bi bi-people"></i> Gestion utilisateurs</a></li>
        {% endif %}
        <li><hr class="dropdown-divider"></li>
        <li><a class="dropdown-item" href="{{ url_for('logout') }}"><i class="bi bi-box-arrow-right"></i> Se déconnecter</a></li>
      </ul>
    </div>
    {% else %}
    <div class="text-center mt-3">
      <a href="{{ url_for('login') }}" class="btn btn-outline-light">
        <i class="bi bi-box-arrow-in-right"></i> Se connecter
      </a>
    </div>
    {% endif %}
  </nav>

  <button id="toggleSidebarBtn" title="Toggle Sidebar">☰</button>

  <div id="content">
    {% block content %}
    {% endblock %}
  </div>

  <script>
    const toggleBtn = document.getElementById('toggleSidebarBtn');
    const sidebar = document.getElementById('sidebar');
    const content = document.getElementById('content');

    toggleBtn.addEventListener('click', () => {
      sidebar.classList.toggle('collapsed');
    });
  </script>

</body>
</html>
