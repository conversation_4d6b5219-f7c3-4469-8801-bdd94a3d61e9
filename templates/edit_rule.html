{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2>✏️ Modifier la règle "{{ rule.name }}"</h2>
<a href="{{ url_for('index') }}" class="btn btn-secondary mb-3">← Accueil</a>

  <div class="card shadow-sm">
    <div class="card-body">
      <form method="POST" novalidate>
        <div class="row g-3 mb-3">
          <div class="col-md-4">
            <label for="name" class="form-label fw-semibold">Nom</label>
            <input
              type="text"
              class="form-control"
              id="name"
              name="name"
              value="{{ rule.name }}"
              placeholder="Nom de la règle"
              required
            >
          </div>
          <div class="col-md-2">
            <label for="metric" class="form-label fw-semibold">Métrique</label>
            <select class="form-select" id="metric" name="metric" required>
              <option value="status" {% if rule.metric == 'status' %}selected{% endif %}>status</option>
              <option value="cpu_load" {% if rule.metric == 'cpu_load' %}selected{% endif %}>cpu_load</option>
              <option value="memory_usage" {% if rule.metric == 'memory_usage' %}selected{% endif %}>memory_usage</option>
              <option value="disk_usage" {% if rule.metric == 'disk_usage' %}selected{% endif %}>disk_usage</option>
              <option value="uptime" {% if rule.metric == 'uptime' %}selected{% endif %}>uptime</option>
            </select>
          </div>
          <div class="col-md-2">
            <label for="operator" class="form-label fw-semibold">Opérateur</label>
            <select class="form-select" id="operator" name="operator" required>
              <option value="==" {% if rule.operator == '==' %}selected{% endif %}>= égale</option>
              <option value="!=" {% if rule.operator == '!=' %}selected{% endif %}>&#8800; différente</option>
              <option value=">" {% if rule.operator == '>' %}selected{% endif %}>&gt; supérieur</option>
              <option value="<" {% if rule.operator == '<' %}selected{% endif %}>&lt; inférieur</option>
              <option value=">=" {% if rule.operator == '>=' %}selected{% endif %}>&ge; supérieur ou égal</option>
              <option value="<=" {% if rule.operator == '<=' %}selected{% endif %}>&le; inférieur ou égal</option>
            </select>
          </div>
          <div class="col-md-2">
            <label for="value" class="form-label fw-semibold">Valeur</label>
            <input
              type="text"
              class="form-control"
              id="value"
              name="value"
              value="{{ rule.value }}"
              placeholder="Valeur à comparer"
              required
            >
          </div>
          <div class="col-md-2">
            <label for="severity" class="form-label fw-semibold">Sévérité</label>
            <select class="form-select" id="severity" name="severity" required>
              <option value="info" {% if rule.severity == 'info' %}selected{% endif %}>Info</option>
              <option value="warning" {% if rule.severity == 'warning' %}selected{% endif %}>Warning</option>
              <option value="average" {% if rule.severity == 'average' %}selected{% endif %}>Average</option>
              <option value="high" {% if rule.severity == 'high' %}selected{% endif %}>High</option>
              <option value="disaster" {% if rule.severity == 'disaster' %}selected{% endif %}>Disaster</option>
            </select>
          </div>
        </div>

        <div class="mb-4">
          <label for="message" class="form-label fw-semibold">Message d’alerte</label>
          <input
            type="text"
            class="form-control"
            id="message"
            name="message"
            value="{{ rule.message }}"
            placeholder="Ex: ⚠️ Machine {{ ip }} est hors ligne"
            required
          >
        </div>

        <button type="submit" class="btn btn-primary me-2">💾 Enregistrer</button>
        <a href="{{ url_for('manage_rules') }}" class="btn btn-outline-secondary">Annuler</a>
      </form>
    </div>
  </div>
</div>
{% endblock %}
