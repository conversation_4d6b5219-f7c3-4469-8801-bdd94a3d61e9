{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2><i class="bi bi-pencil-square"></i> Modifier l'utilisateur</h2>
  <a href="{{ url_for('manage_users') }}" class="btn btn-secondary mb-3">← Retour aux utilisateurs</a>

  <!-- Messages Flash -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          {{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0"><i class="bi bi-person-badge"></i> Modifier l'utilisateur : {{ user.username }}</h5>
        </div>
        <div class="card-body">
          <form method="POST">
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="username" class="form-label">Nom d'utilisateur *</label>
                  <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-person"></i></span>
                    <input type="text" class="form-control" id="username" name="username" value="{{ user.username }}" required>
                  </div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="email" class="form-label">Adresse email *</label>
                  <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                    <input type="email" class="form-control" id="email" name="email" value="{{ user.email }}" required>
                  </div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="new_password" class="form-label">Nouveau mot de passe</label>
                  <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-lock"></i></span>
                    <input type="password" class="form-control" id="new_password" name="new_password">
                  </div>
                  <div class="form-text">Laissez vide pour conserver le mot de passe actuel</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="role" class="form-label">Rôle *</label>
                  <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-shield"></i></span>
                    <select class="form-select" id="role" name="role" required>
                      <option value="viewer" {% if user.role == 'viewer' %}selected{% endif %}>Viewer - Lecture seule</option>
                      <option value="user" {% if user.role == 'user' %}selected{% endif %}>User - Lecture + Écriture</option>
                      <option value="admin" {% if user.role == 'admin' %}selected{% endif %}>Admin - Tous droits</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            <div class="mb-3">
              <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="active" name="active" {% if user.active %}checked{% endif %}>
                <label class="form-check-label" for="active">Compte actif</label>
              </div>
              <div class="form-text">Désactivez pour bloquer temporairement l'accès sans supprimer le compte</div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
              <a href="{{ url_for('manage_users') }}" class="btn btn-outline-secondary me-md-2">
                <i class="bi bi-x-circle"></i> Annuler
              </a>
              <button type="submit" class="btn btn-primary">
                <i class="bi bi-check-circle"></i> Enregistrer les modifications
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Informations supplémentaires -->
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="mb-0"><i class="bi bi-info-circle"></i> Informations du compte</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <ul class="list-group list-group-flush">
                <li class="list-group-item d-flex justify-content-between align-items-center">
                  <span>ID utilisateur</span>
                  <span class="text-muted">{{ user.id }}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                  <span>Compte créé le</span>
                  <span class="text-muted">{{ user.created_at.strftime('%d/%m/%Y à %H:%M') }}</span>
                </li>
                <li class="list-group-item d-flex justify-content-between align-items-center">
                  <span>Dernière connexion</span>
                  <span class="text-muted">{{ user.last_login.strftime('%d/%m/%Y à %H:%M') if user.last_login else 'Jamais' }}</span>
                </li>
              </ul>
            </div>
            <div class="col-md-6">
              <div class="alert alert-warning mb-0">
                <h6><i class="bi bi-exclamation-triangle"></i> Attention</h6>
                <ul class="mb-0">
                  <li>La modification du rôle change les permissions</li>
                  <li>Désactiver un compte bloque l'accès immédiatement</li>
                  <li>Le changement de mot de passe prend effet immédiatement</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
