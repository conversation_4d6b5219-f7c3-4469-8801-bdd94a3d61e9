{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2>⚙️ Configuration du Système</h2>
  <a href="{{ url_for('index') }}" class="btn btn-secondary mb-3">← Accueil</a>

  <form method="POST">
    <!-- Configuration Email -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">📧 Configuration Email (SMTP)</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label for="smtp_server" class="form-label">Serveur SMTP</label>
              <input type="text" class="form-control" name="config_smtp_server" 
                     value="{{ configs.smtp_server.value if configs.smtp_server else '' }}" 
                     placeholder="smtp.gmail.com">
              <div class="form-text">{{ configs.smtp_server.description if configs.smtp_server else '' }}</div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label for="smtp_port" class="form-label">Port SMTP</label>
              <input type="number" class="form-control" name="config_smtp_port" 
                     value="{{ configs.smtp_port.value if configs.smtp_port else '' }}" 
                     placeholder="587">
              <div class="form-text">{{ configs.smtp_port.description if configs.smtp_port else '' }}</div>
            </div>
          </div>
        </div>
        <div class="row">
          <div class="col-md-6">
            <div class="mb-3">
              <label for="smtp_user" class="form-label">Nom d'utilisateur SMTP</label>
              <input type="email" class="form-control" name="config_smtp_user" 
                     value="{{ configs.smtp_user.value if configs.smtp_user else '' }}" 
                     placeholder="<EMAIL>">
              <div class="form-text">{{ configs.smtp_user.description if configs.smtp_user else '' }}</div>
            </div>
          </div>
          <div class="col-md-6">
            <div class="mb-3">
              <label for="smtp_password" class="form-label">Mot de passe SMTP</label>
              <input type="password" class="form-control" name="config_smtp_password" 
                     value="{{ configs.smtp_password.value if configs.smtp_password else '' }}" 
                     placeholder="Mot de passe ou token d'application">
              <div class="form-text">{{ configs.smtp_password.description if configs.smtp_password else '' }}</div>
            </div>
          </div>
        </div>
        <div class="mb-3">
          <label for="email_from" class="form-label">Adresse expéditeur</label>
          <input type="email" class="form-control" name="config_email_from" 
                 value="{{ configs.email_from.value if configs.email_from else '' }}" 
                 placeholder="<EMAIL>">
          <div class="form-text">{{ configs.email_from.description if configs.email_from else '' }}</div>
        </div>
        <div class="d-flex gap-2">
          <button type="button" id="testEmailBtn" class="btn btn-outline-primary">📧 Tester la configuration</button>
          <div id="emailTestResult" class="align-self-center"></div>
        </div>
      </div>
    </div>

    <!-- Configuration Monitoring -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">🔍 Configuration Monitoring</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-4">
            <div class="mb-3">
              <label for="scan_interval" class="form-label">Intervalle de scan (secondes)</label>
              <input type="number" class="form-control" name="config_scan_interval" 
                     value="{{ configs.scan_interval.value if configs.scan_interval else '' }}" 
                     placeholder="300" min="60">
              <div class="form-text">{{ configs.scan_interval.description if configs.scan_interval else '' }}</div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              <label for="snmp_timeout" class="form-label">Timeout SNMP (secondes)</label>
              <input type="number" class="form-control" name="config_snmp_timeout" 
                     value="{{ configs.snmp_timeout.value if configs.snmp_timeout else '' }}" 
                     placeholder="2" min="1" max="10">
              <div class="form-text">{{ configs.snmp_timeout.description if configs.snmp_timeout else '' }}</div>
            </div>
          </div>
          <div class="col-md-4">
            <div class="mb-3">
              <label for="snmp_retries" class="form-label">Tentatives SNMP</label>
              <input type="number" class="form-control" name="config_snmp_retries" 
                     value="{{ configs.snmp_retries.value if configs.snmp_retries else '' }}" 
                     placeholder="1" min="0" max="5">
              <div class="form-text">{{ configs.snmp_retries.description if configs.snmp_retries else '' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Configuration Données -->
    <div class="card mb-4">
      <div class="card-header">
        <h5 class="mb-0">💾 Configuration Données</h5>
      </div>
      <div class="card-body">
        <div class="mb-3">
          <label for="data_retention_days" class="form-label">Rétention des données (jours)</label>
          <input type="number" class="form-control" name="config_data_retention_days" 
                 value="{{ configs.data_retention_days.value if configs.data_retention_days else '' }}" 
                 placeholder="30" min="1" max="365">
          <div class="form-text">{{ configs.data_retention_days.description if configs.data_retention_days else '' }}</div>
        </div>
        
        <div class="alert alert-info">
          <h6>Informations sur la base de données:</h6>
          <ul class="mb-0">
            <li>Emplacement: <code>instance/monitoring.db</code></li>
            <li>Type: SQLite</li>
            <li>Nettoyage automatique: Activé (selon la rétention configurée)</li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Boutons d'action -->
    <div class="d-flex gap-2 mb-4">
      <button type="submit" class="btn btn-primary">💾 Enregistrer la configuration</button>
      <button type="button" class="btn btn-outline-secondary" onclick="location.reload()">🔄 Annuler</button>
    </div>
  </form>

  <!-- Informations système -->
  <div class="card">
    <div class="card-header">
      <h5 class="mb-0">ℹ️ Informations Système</h5>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-6">
          <h6>Application:</h6>
          <ul class="list-unstyled">
            <li><strong>Version:</strong> 1.0.0</li>
            <li><strong>Framework:</strong> Flask</li>
            <li><strong>Base de données:</strong> SQLite</li>
            <li><strong>Mode debug:</strong> {{ config.DEBUG }}</li>
          </ul>
        </div>
        <div class="col-md-6">
          <h6>Dernières mises à jour:</h6>
          <ul class="list-unstyled">
            {% for key, config_item in configs.items() %}
            {% if config_item.updated_at %}
            <li><strong>{{ key }}:</strong> {{ config_item.updated_at.strftime('%d/%m/%Y %H:%M') }}</li>
            {% endif %}
            {% endfor %}
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.getElementById('testEmailBtn').addEventListener('click', function() {
  const btn = this;
  const result = document.getElementById('emailTestResult');
  
  btn.disabled = true;
  btn.innerHTML = '⏳ Test en cours...';
  result.innerHTML = '';
  
  fetch('/config/test-email', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    }
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      result.innerHTML = '<span class="text-success">✅ ' + data.message + '</span>';
    } else {
      result.innerHTML = '<span class="text-danger">❌ ' + data.message + '</span>';
    }
  })
  .catch(error => {
    result.innerHTML = '<span class="text-danger">❌ Erreur de connexion</span>';
  })
  .finally(() => {
    btn.disabled = false;
    btn.innerHTML = '📧 Tester la configuration';
  });
});
</script>
{% endblock %}
