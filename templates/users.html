{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2><i class="bi bi-people"></i> Gestion des Utilisateurs</h2>
  <a href="{{ url_for('index') }}" class="btn btn-secondary mb-3">← Accueil</a>

  <!-- Messages Flash -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          {{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <!-- Bouton d'ajout -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h3>Liste des utilisateurs ({{ users|length }})</h3>
    <a href="{{ url_for('create_user') }}" class="btn btn-success">
      <i class="bi bi-person-plus"></i> Nouvel utilisateur
    </a>
  </div>

  <!-- Tableau des utilisateurs -->
  <div class="card shadow-sm">
    <div class="card-body">
      <div class="table-responsive">
        <table class="table table-striped table-hover">
          <thead class="table-dark">
            <tr>
              <th>Utilisateur</th>
              <th>Email</th>
              <th>Rôle</th>
              <th>Statut</th>
              <th>Créé le</th>
              <th>Dernière connexion</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for user in users %}
            <tr>
              <td>
                <div class="d-flex align-items-center">
                  <div class="bg-light rounded-circle p-2 me-2">
                    <i class="bi bi-person-fill text-primary"></i>
                  </div>
                  <div>
                    <strong>{{ user.username }}</strong>
                    {% if user.id == current_user.id %}
                      <span class="badge bg-info">Vous</span>
                    {% endif %}
                  </div>
                </div>
              </td>
              <td>{{ user.email }}</td>
              <td>
                <span class="badge {% if user.role == 'admin' %}bg-danger{% elif user.role == 'user' %}bg-success{% else %}bg-info{% endif %}">
                  {% if user.role == 'admin' %}
                    <i class="bi bi-shield-fill"></i> Admin
                  {% elif user.role == 'user' %}
                    <i class="bi bi-person-check"></i> User
                  {% else %}
                    <i class="bi bi-eye"></i> Viewer
                  {% endif %}
                </span>
              </td>
              <td>
                <span class="badge {% if user.active %}bg-success{% else %}bg-secondary{% endif %}">
                  {% if user.active %}
                    <i class="bi bi-check-circle"></i> Actif
                  {% else %}
                    <i class="bi bi-x-circle"></i> Inactif
                  {% endif %}
                </span>
              </td>
              <td>{{ user.created_at.strftime('%d/%m/%Y') }}</td>
              <td>
                {% if user.last_login %}
                  {{ user.last_login.strftime('%d/%m/%Y %H:%M') }}
                {% else %}
                  <span class="text-muted">Jamais</span>
                {% endif %}
              </td>
              <td>
                <div class="btn-group" role="group">
                  <a href="{{ url_for('edit_user', user_id=user.id) }}" class="btn btn-sm btn-outline-primary">
                    <i class="bi bi-pencil"></i>
                  </a>
                  {% if user.id != current_user.id %}
                    <form action="{{ url_for('delete_user', user_id=user.id) }}" method="post" 
                          onsubmit="return confirm('Êtes-vous sûr de vouloir supprimer cet utilisateur ?');" class="d-inline">
                      <button class="btn btn-sm btn-outline-danger" type="submit">
                        <i class="bi bi-trash"></i>
                      </button>
                    </form>
                  {% endif %}
                </div>
              </td>
            </tr>
            {% else %}
            <tr>
              <td colspan="7" class="text-center">Aucun utilisateur trouvé</td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>

  <!-- Statistiques -->
  <div class="row mt-4">
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h5 class="card-title text-danger">{{ users|selectattr('role', 'equalto', 'admin')|list|length }}</h5>
          <p class="card-text">Administrateurs</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h5 class="card-title text-success">{{ users|selectattr('role', 'equalto', 'user')|list|length }}</h5>
          <p class="card-text">Utilisateurs</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h5 class="card-title text-info">{{ users|selectattr('role', 'equalto', 'viewer')|list|length }}</h5>
          <p class="card-text">Observateurs</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h5 class="card-title text-primary">{{ users|selectattr('active', 'equalto', true)|list|length }}</h5>
          <p class="card-text">Comptes actifs</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Informations sur les rôles -->
  <div class="card mt-4">
    <div class="card-header">
      <h5 class="mb-0"><i class="bi bi-info-circle"></i> Rôles et permissions</h5>
    </div>
    <div class="card-body">
      <div class="row">
        <div class="col-md-4">
          <h6><span class="badge bg-danger">Admin</span></h6>
          <ul class="list-unstyled">
            <li><i class="bi bi-check text-success"></i> Lecture complète</li>
            <li><i class="bi bi-check text-success"></i> Écriture complète</li>
            <li><i class="bi bi-check text-success"></i> Suppression</li>
            <li><i class="bi bi-check text-success"></i> Configuration système</li>
            <li><i class="bi bi-check text-success"></i> Gestion utilisateurs</li>
          </ul>
        </div>
        <div class="col-md-4">
          <h6><span class="badge bg-success">User</span></h6>
          <ul class="list-unstyled">
            <li><i class="bi bi-check text-success"></i> Lecture complète</li>
            <li><i class="bi bi-check text-success"></i> Écriture (scan, alertes)</li>
            <li><i class="bi bi-x text-danger"></i> Suppression</li>
            <li><i class="bi bi-x text-danger"></i> Configuration système</li>
            <li><i class="bi bi-x text-danger"></i> Gestion utilisateurs</li>
          </ul>
        </div>
        <div class="col-md-4">
          <h6><span class="badge bg-info">Viewer</span></h6>
          <ul class="list-unstyled">
            <li><i class="bi bi-check text-success"></i> Lecture seulement</li>
            <li><i class="bi bi-x text-danger"></i> Écriture</li>
            <li><i class="bi bi-x text-danger"></i> Suppression</li>
            <li><i class="bi bi-x text-danger"></i> Configuration système</li>
            <li><i class="bi bi-x text-danger"></i> Gestion utilisateurs</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
