{% extends "base.html" %}

{% block content %}
<div class="container py-4 fade-in-up">
  <!-- Messages Flash -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          <i class="bi bi-info-circle me-2"></i>{{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <!-- Header moderne -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card" style="background: linear-gradient(135deg, #f56565 0%, #e53e3e 100%); color: white; border: none;">
        <div class="card-body text-center py-5">
          <h1 class="display-4 fw-bold mb-3">
            <i class="bi bi-exclamation-triangle me-3 pulse-animation"></i>Centre d'Alertes
          </h1>
          <p class="lead mb-4 opacity-90">Surveillance et gestion des incidents réseau</p>
          <a href="{{ url_for('dashboard') }}" class="btn btn-light btn-lg shadow">
            <i class="bi bi-speedometer2 me-2"></i>Retour au Dashboard
          </a>
        </div>
      </div>
    </div>
  </div>

  <!-- Statistiques des alertes -->
  <div class="row mb-4">
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card text-center">
        <div class="card-body">
          <i class="bi bi-exclamation-triangle-fill text-danger" style="font-size: 2rem;"></i>
          <h4 class="mt-2">{{ alerts|selectattr('acknowledged', 'equalto', false)|list|length }}</h4>
          <p class="text-muted">Alertes actives</p>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card text-center">
        <div class="card-body">
          <i class="bi bi-check-circle-fill text-success" style="font-size: 2rem;"></i>
          <h4 class="mt-2">{{ alerts|selectattr('acknowledged', 'equalto', true)|list|length }}</h4>
          <p class="text-muted">Alertes traitées</p>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card text-center">
        <div class="card-body">
          <i class="bi bi-fire text-danger" style="font-size: 2rem;"></i>
          <h4 class="mt-2">{{ alerts|selectattr('severity', 'equalto', 'disaster')|list|length }}</h4>
          <p class="text-muted">Critiques</p>
        </div>
      </div>
    </div>
    <div class="col-lg-3 col-md-6 mb-3">
      <div class="card text-center">
        <div class="card-body">
          <i class="bi bi-list-ul text-primary" style="font-size: 2rem;"></i>
          <h4 class="mt-2">{{ alerts|length }}</h4>
          <p class="text-muted">Total alertes</p>
        </div>
      </div>
    </div>
  </div>

  <table class="table table-bordered table-striped">
    <thead class="table-dark">
      <tr>
        <th>Sévérité</th>
        <th>IP</th>
        <th>Nom</th>
        <th>Statut</th>
        <th>Message</th>
        <th>Date</th>
        <th>Pris en compte</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
      {% for alert in alerts %}
      <tr class="{% if alert.severity == 'disaster' %}table-danger{% elif alert.severity == 'high' %}table-warning{% elif alert.severity == 'average' %}table-info{% endif %}">
        <td>
          {% if alert.severity == 'disaster' %}
            <span class="badge bg-danger">🔥 DISASTER</span>
          {% elif alert.severity == 'high' %}
            <span class="badge bg-warning text-dark">⚠️ HIGH</span>
          {% elif alert.severity == 'average' %}
            <span class="badge bg-info">📊 AVERAGE</span>
          {% elif alert.severity == 'warning' %}
            <span class="badge bg-secondary">⚡ WARNING</span>
          {% else %}
            <span class="badge bg-light text-dark">ℹ️ INFO</span>
          {% endif %}
        </td>
        <td>{{ alert.ip }}</td>
        <td>{{ alert.name }}</td>
        <td>
          {% if alert.status == "up" %}
            <span class="badge bg-success">En ligne</span>
          {% else %}
            <span class="badge bg-danger">Hors ligne</span>
          {% endif %}
        </td>
        <td>{{ alert.message }}</td>
        <td>{{ alert.timestamp.strftime('%d/%m/%Y %H:%M:%S') }}</td>
        <td>
          {% if alert.acknowledged %}
            <span class="text-success">✅ Oui</span><br>
            <small class="text-muted">{{ alert.ack_timestamp.strftime('%d/%m/%Y %H:%M:%S') }}</small><br>
            <small class="text-muted">par {{ alert.ack_user }}</small>
          {% else %}
            <span class="text-danger">❌ Non</span>
          {% endif %}
        </td>
        <td>
          {% if not alert.acknowledged %}
            <form action="{{ url_for('acknowledge_alert', alert_id=alert.id) }}" method="post" style="margin:0;" onsubmit="return confirm('Confirmer la prise en compte de cette alerte ?');">
              <button class="btn btn-sm btn-success" type="submit">✅ Acknowledge</button>
            </form>
          {% else %}
            <button class="btn btn-sm btn-secondary" disabled>Pris en compte</button>
          {% endif %}
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
{% endblock %}
