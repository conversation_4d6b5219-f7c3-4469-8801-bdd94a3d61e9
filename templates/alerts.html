{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2>📢 Historique des Alertes</h2>

  <a href="{{ url_for('index') }}" class="btn btn-secondary mb-3">← Accueil</a>

  <table class="table table-bordered table-striped">
    <thead class="table-dark">
      <tr>
        <th>IP</th>
        <th>Nom</th>
        <th>Statut</th>
        <th>Message</th>
        <th>Date</th>
        <th>Pris en compte</th>
        <th>Action</th>
      </tr>
    </thead>
    <tbody>
      {% for alert in alerts %}
      <tr>
        <td>{{ alert.ip }}</td>
        <td>{{ alert.name }}</td>
        <td>
          {% if alert.status == "up" %}
            <span class="badge bg-success">En ligne</span>
          {% else %}
            <span class="badge bg-danger">Hors ligne</span>
          {% endif %}
        </td>
        <td>{{ alert.message }}</td>
        <td>{{ alert.timestamp.strftime('%d/%m/%Y %H:%M:%S') }}</td>
        <td>
          {% if alert.acknowledged %}
            Oui ({{ alert.ack_timestamp.strftime('%d/%m/%Y %H:%M:%S') }})
          {% else %}
            Non
          {% endif %}
        </td>
        <td>
          {% if not alert.acknowledged %}
            <form action="{{ url_for('acknowledge_alert', alert_id=alert.id) }}" method="post" style="margin:0;" onsubmit="return confirm('Confirmer la prise en compte de cette alerte ?');">
              <button class="btn btn-sm btn-success" type="submit">Acknowledge</button>
            </form>
          {% else %}
            <button class="btn btn-sm btn-secondary" disabled>Pris en compte</button>
          {% endif %}
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
{% endblock %}
