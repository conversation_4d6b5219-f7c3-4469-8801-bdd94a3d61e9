{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2>📈 Détails pour {{ machine_name }} ({{ machine_ip }})</h2>
  <a href="{{ url_for('index') }}" class="btn btn-secondary mb-3">← Accueil</a>

  <!-- Informations système -->
  <div class="card mb-4">
    <div class="card-body">
      <h5 class="card-title">Informations Système</h5>
      <p><strong>Description :</strong> {{ machine_description }}</p>
    </div>
  </div>

  <!-- Graphiques de métriques -->
  <div class="row mb-4">
    <div class="col-md-6">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Historique CPU (%)</h5>
          <canvas id="cpuLoadChart" width="400" height="200"
                  data-labels="{{ chart_labels|tojson }}"
                  data-cpu-data="{{ chart_cpu_data|tojson }}"></canvas>
        </div>
      </div>
    </div>
    <div class="col-md-6">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Historique Mémoire (%)</h5>
          <canvas id="memoryChart" width="400" height="200"
                  data-labels="{{ chart_labels|tojson }}"
                  data-memory-data="{{ chart_memory_data|tojson }}"></canvas>
        </div>
      </div>
    </div>
  </div>

  <div class="row mb-4">
    <div class="col-md-12">
      <div class="card">
        <div class="card-body">
          <h5 class="card-title">Historique Disque (%)</h5>
          <canvas id="diskChart" width="400" height="200"
                  data-labels="{{ chart_labels|tojson }}"
                  data-disk-data="{{ chart_disk_data|tojson }}"></canvas>
        </div>
      </div>
    </div>
  </div>

  <!-- Tableau de l'historique -->
  <div class="card">
    <div class="card-body">
      <h5 class="card-title">Historique des scans</h5>
      <table class="table table-bordered table-striped">
        <thead class="table-dark">
          <tr>
            <th>Timestamp</th>
            <th>Status</th>
            <th>Nom</th>
            <th>CPU (%)</th>
            <th>Mémoire (%)</th>
            <th>Disque (%)</th>
            <th>Uptime</th>
          </tr>
        </thead>
        <tbody>
          {% for entry in history|reverse %}
          <tr>
            <td>{{ entry.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</td>
            <td>
              {% if entry.status == 'up' %}
                <span class="badge bg-success">En ligne</span>
              {% else %}
                <span class="badge bg-danger">Hors ligne</span>
              {% endif %}
            </td>
            <td>{{ entry.name }}</td>
            <td>
              {% if entry.cpu_load is not none %}
                <span class="badge {% if entry.cpu_load > 80 %}bg-danger{% elif entry.cpu_load > 60 %}bg-warning{% else %}bg-success{% endif %}">{{ entry.cpu_load }}%</span>
              {% else %}
                <span class="text-muted">N/A</span>
              {% endif %}
            </td>
            <td>
              {% if entry.memory_usage is not none %}
                <span class="badge {% if entry.memory_usage > 80 %}bg-danger{% elif entry.memory_usage > 60 %}bg-warning{% else %}bg-success{% endif %}">{{ entry.memory_usage }}%</span>
              {% else %}
                <span class="text-muted">N/A</span>
              {% endif %}
            </td>
            <td>
              {% if entry.disk_usage is not none %}
                <span class="badge {% if entry.disk_usage > 80 %}bg-danger{% elif entry.disk_usage > 60 %}bg-warning{% else %}bg-success{% endif %}">{{ entry.disk_usage }}%</span>
              {% else %}
                <span class="text-muted">N/A</span>
              {% endif %}
            </td>
            <td>
              {% if entry.uptime is not none %}
                {% set days = (entry.uptime // 86400) %}
                {% set hours = ((entry.uptime % 86400) // 3600) %}
                {% set minutes = ((entry.uptime % 3600) // 60) %}
                <small>{{ days }}d {{ hours }}h {{ minutes }}m</small>
              {% else %}
                <span class="text-muted">N/A</span>
              {% endif %}
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  // Get data from canvas elements
  const cpuCanvas = document.getElementById('cpuLoadChart');
  const memoryCanvas = document.getElementById('memoryChart');
  const diskCanvas = document.getElementById('diskChart');

  const labels = JSON.parse(cpuCanvas.dataset.labels);
  const cpuData = JSON.parse(cpuCanvas.dataset.cpuData);
  const memoryData = JSON.parse(memoryCanvas.dataset.memoryData);
  const diskData = JSON.parse(diskCanvas.dataset.diskData);

  // CPU Chart
  const cpuCtx = cpuCanvas.getContext('2d');
  const cpuLoadChart = new Chart(cpuCtx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: 'CPU (%)',
        data: cpuData,
        borderColor: 'rgb(75, 192, 192)',
        backgroundColor: 'rgba(75, 192, 192, 0.1)',
        tension: 0.1,
        fill: true
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true,
          suggestedMax: 100
        }
      }
    }
  });

  // Memory Chart
  const memoryCtx = memoryCanvas.getContext('2d');
  const memoryChart = new Chart(memoryCtx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: 'Mémoire (%)',
        data: memoryData,
        borderColor: 'rgb(255, 99, 132)',
        backgroundColor: 'rgba(255, 99, 132, 0.1)',
        tension: 0.1,
        fill: true
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true,
          suggestedMax: 100
        }
      }
    }
  });

  // Disk Chart
  const diskCtx = diskCanvas.getContext('2d');
  const diskChart = new Chart(diskCtx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: 'Disque (%)',
        data: diskData,
        borderColor: 'rgb(255, 205, 86)',
        backgroundColor: 'rgba(255, 205, 86, 0.1)',
        tension: 0.1,
        fill: true
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true,
          suggestedMax: 100
        }
      }
    }
  });
</script>
{% endblock %}
