{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2>📈 Détails pour {{ machine_name }} ({{ machine_ip }})</h2>
  <a href="{{ url_for('index') }}" class="btn btn-secondary mb-3">← Accueil</a>

  <!-- Informations système -->
  <div class="card mb-4">
    <div class="card-body">
      <h5 class="card-title">Informations Système</h5>
      <p><strong>Description :</strong> {{ machine_description }}</p>
    </div>
  </div>

  <!-- Graphique de charge CPU -->
  <div class="card mb-4">
    <div class="card-body">
      <h5 class="card-title">Historique de la charge CPU (%)</h5>
      <canvas id="cpuLoadChart" width="400" height="150"
              data-labels="{{ chart_labels|tojson }}"
              data-cpu-data="{{ chart_cpu_data|tojson }}"></canvas>
    </div>
  </div>

  <!-- Tableau de l'historique -->
  <div class="card">
    <div class="card-body">
      <h5 class="card-title">Historique des scans</h5>
      <table class="table table-bordered table-striped">
        <thead class="table-dark">
          <tr>
            <th>Timestamp</th>
            <th>Status</th>
            <th>Nom</th>
            <th>Charge CPU (%)</th>
          </tr>
        </thead>
        <tbody>
          {% for entry in history|reverse %}
          <tr>
            <td>{{ entry.timestamp.strftime('%Y-%m-%d %H:%M:%S') }}</td>
            <td>
              {% if entry.status == 'up' %}
                <span class="badge bg-success">En ligne</span>
              {% else %}
                <span class="badge bg-danger">Hors ligne</span>
              {% endif %}
            </td>
            <td>{{ entry.name }}</td>
            <td>
              {% if entry.cpu_load is not none %}
                {{ entry.cpu_load }}%
              {% else %}
                N/A
              {% endif %}
            </td>
          </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script>
  const chartCanvas = document.getElementById('cpuLoadChart');
  const labels = JSON.parse(chartCanvas.dataset.labels);
  const cpuData = JSON.parse(chartCanvas.dataset.cpuData);

  const ctx = chartCanvas.getContext('2d');
  const cpuLoadChart = new Chart(ctx, {
    type: 'line',
    data: {
      labels: labels,
      datasets: [{
        label: 'Charge CPU (%)',
        data: cpuData,
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1,
        fill: false
      }]
    },
    options: {
      responsive: true,
      scales: {
        y: {
          beginAtZero: true,
          suggestedMax: 100
        }
      }
    }
  });
</script>
{% endblock %}
