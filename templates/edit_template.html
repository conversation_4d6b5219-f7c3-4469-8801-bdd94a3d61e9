{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2>✏️ Modifier le template "{{ template.name }}"</h2>
  <a href="{{ url_for('manage_templates') }}" class="btn btn-secondary mb-3">← Retour aux templates</a>

  <div class="card shadow-sm">
    <div class="card-body">
      <form method="POST">
        <div class="mb-3">
          <label for="name" class="form-label fw-semibold">Nom du template</label>
          <input
            type="text"
            class="form-control"
            id="name"
            name="name"
            value="{{ template.name }}"
            placeholder="Nom du template"
            required
          >
        </div>
        
        <div class="mb-4">
          <label for="description" class="form-label fw-semibold">Description</label>
          <textarea
            class="form-control"
            id="description"
            name="description"
            rows="3"
            placeholder="Description du template (optionnel)"
          >{{ template.description or '' }}</textarea>
        </div>

        <div class="mb-4">
          <h6>Informations:</h6>
          <ul class="list-unstyled">
            <li><strong>Créé le:</strong> {{ template.created_at.strftime('%d/%m/%Y à %H:%M') }}</li>
            <li><strong>Nombre de machines:</strong> {{ template.machines|length }}</li>
            <li><strong>Nombre d'items:</strong> {{ template.items|length }}</li>
            <li><strong>Nombre de règles:</strong> {{ template.alert_rules|length }}</li>
          </ul>
        </div>

        {% if template.machines %}
        <div class="mb-4">
          <h6>Machines utilisant ce template:</h6>
          <div class="table-responsive">
            <table class="table table-sm table-striped">
              <thead>
                <tr>
                  <th>IP</th>
                  <th>Nom</th>
                  <th>Statut</th>
                  <th>Groupe</th>
                  <th>Dernière mise à jour</th>
                </tr>
              </thead>
              <tbody>
                {% for machine in template.machines %}
                <tr>
                  <td><a href="{{ url_for('machine_detail', ip=machine.ip) }}">{{ machine.ip }}</a></td>
                  <td>{{ machine.name }}</td>
                  <td>
                    {% if machine.status == "up" %}
                      <span class="badge bg-success">En ligne</span>
                    {% else %}
                      <span class="badge bg-danger">Hors ligne</span>
                    {% endif %}
                  </td>
                  <td>
                    {% if machine.host_group %}
                      <span class="badge bg-primary">{{ machine.host_group.name }}</span>
                    {% else %}
                      <span class="text-muted">Aucun</span>
                    {% endif %}
                  </td>
                  <td>{{ machine.timestamp.strftime('%d/%m/%Y %H:%M') }}</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
        {% endif %}

        {% if template.items %}
        <div class="mb-4">
          <h6>Items de monitoring:</h6>
          <div class="table-responsive">
            <table class="table table-sm table-striped">
              <thead>
                <tr>
                  <th>Nom</th>
                  <th>Clé</th>
                  <th>Type</th>
                  <th>Unités</th>
                  <th>Actif</th>
                </tr>
              </thead>
              <tbody>
                {% for item in template.items %}
                <tr>
                  <td>{{ item.name }}</td>
                  <td><code>{{ item.key }}</code></td>
                  <td>{{ item.value_type }}</td>
                  <td>{{ item.units or '-' }}</td>
                  <td>
                    {% if item.active %}
                      <span class="badge bg-success">Oui</span>
                    {% else %}
                      <span class="badge bg-secondary">Non</span>
                    {% endif %}
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
        {% endif %}

        {% if template.alert_rules %}
        <div class="mb-4">
          <h6>Règles d'alerte:</h6>
          <div class="table-responsive">
            <table class="table table-sm table-striped">
              <thead>
                <tr>
                  <th>Nom</th>
                  <th>Condition</th>
                  <th>Sévérité</th>
                  <th>Actif</th>
                </tr>
              </thead>
              <tbody>
                {% for rule in template.alert_rules %}
                <tr>
                  <td>{{ rule.name }}</td>
                  <td>{{ rule.metric }} {{ rule.operator }} {{ rule.value }}</td>
                  <td>
                    {% if rule.severity == 'disaster' %}
                      <span class="badge bg-danger">🔥 DISASTER</span>
                    {% elif rule.severity == 'high' %}
                      <span class="badge bg-warning text-dark">⚠️ HIGH</span>
                    {% elif rule.severity == 'average' %}
                      <span class="badge bg-info">📊 AVERAGE</span>
                    {% elif rule.severity == 'warning' %}
                      <span class="badge bg-secondary">⚡ WARNING</span>
                    {% else %}
                      <span class="badge bg-light text-dark">ℹ️ INFO</span>
                    {% endif %}
                  </td>
                  <td>
                    {% if rule.active %}
                      <span class="badge bg-success">Oui</span>
                    {% else %}
                      <span class="badge bg-secondary">Non</span>
                    {% endif %}
                  </td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
        {% endif %}

        <button type="submit" class="btn btn-primary me-2">💾 Enregistrer</button>
        <a href="{{ url_for('manage_templates') }}" class="btn btn-outline-secondary">Annuler</a>
      </form>
    </div>
  </div>
</div>
{% endblock %}
