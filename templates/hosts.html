{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2>🖥️ Gestion des Hôtes</h2>
  <a href="{{ url_for('index') }}" class="btn btn-secondary mb-3">← Accueil</a>

  <!-- Filtres -->
  <div class="card mb-4">
    <div class="card-body">
      <div class="row">
        <div class="col-md-3">
          <label class="form-label">Filtrer par groupe:</label>
          <select id="groupFilter" class="form-select">
            <option value="">Tous les groupes</option>
            {% for group in groups %}
            <option value="{{ group.id }}">{{ group.name }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="col-md-3">
          <label class="form-label">Filtrer par template:</label>
          <select id="templateFilter" class="form-select">
            <option value="">Tous les templates</option>
            {% for template in templates %}
            <option value="{{ template.id }}">{{ template.name }}</option>
            {% endfor %}
          </select>
        </div>
        <div class="col-md-3">
          <label class="form-label">Filtrer par statut:</label>
          <select id="statusFilter" class="form-select">
            <option value="">Tous les statuts</option>
            <option value="up">En ligne</option>
            <option value="down">Hors ligne</option>
          </select>
        </div>
        <div class="col-md-3 d-flex align-items-end">
          <button id="clearFilters" class="btn btn-outline-secondary">🗑 Effacer filtres</button>
        </div>
      </div>
    </div>
  </div>

  <!-- Liste des hôtes -->
  <div class="card">
    <div class="card-body">
      <h5 class="card-title">📋 Hôtes découverts ({{ hosts|length }} total)</h5>
      <div class="table-responsive">
        <table id="hostsTable" class="table table-striped table-hover">
          <thead class="table-dark">
            <tr>
              <th>IP</th>
              <th>Nom</th>
              <th>Statut</th>
              <th>Groupe</th>
              <th>Template</th>
              <th>CPU</th>
              <th>Mémoire</th>
              <th>Disque</th>
              <th>Dernière mise à jour</th>
              <th>Actions</th>
            </tr>
          </thead>
          <tbody>
            {% for host in hosts %}
            <tr data-group="{{ host.host_group_id or '' }}" 
                data-template="{{ host.template_id or '' }}" 
                data-status="{{ host.status }}">
              <td><a href="{{ url_for('machine_detail', ip=host.ip) }}">{{ host.ip }}</a></td>
              <td>{{ host.name }}</td>
              <td>
                {% if host.status == "up" %}
                  <span class="badge bg-success">En ligne</span>
                {% else %}
                  <span class="badge bg-danger">Hors ligne</span>
                {% endif %}
              </td>
              <td>
                {% if host.host_group %}
                  <span class="badge bg-primary">{{ host.host_group.name }}</span>
                {% else %}
                  <span class="text-muted">Aucun</span>
                {% endif %}
              </td>
              <td>
                {% if host.template %}
                  <span class="badge bg-info">{{ host.template.name }}</span>
                {% else %}
                  <span class="text-muted">Aucun</span>
                {% endif %}
              </td>
              <td>
                {% if host.cpu_load is not none %}
                  <span class="badge {% if host.cpu_load > 80 %}bg-danger{% elif host.cpu_load > 60 %}bg-warning{% else %}bg-success{% endif %}">{{ host.cpu_load }}%</span>
                {% else %}
                  <span class="text-muted">N/A</span>
                {% endif %}
              </td>
              <td>
                {% if host.memory_usage is not none %}
                  <span class="badge {% if host.memory_usage > 80 %}bg-danger{% elif host.memory_usage > 60 %}bg-warning{% else %}bg-success{% endif %}">{{ host.memory_usage }}%</span>
                {% else %}
                  <span class="text-muted">N/A</span>
                {% endif %}
              </td>
              <td>
                {% if host.disk_usage is not none %}
                  <span class="badge {% if host.disk_usage > 80 %}bg-danger{% elif host.disk_usage > 60 %}bg-warning{% else %}bg-success{% endif %}">{{ host.disk_usage }}%</span>
                {% else %}
                  <span class="text-muted">N/A</span>
                {% endif %}
              </td>
              <td>{{ host.timestamp.strftime('%d/%m/%Y %H:%M') }}</td>
              <td>
                <button class="btn btn-sm btn-outline-primary" data-bs-toggle="modal" data-bs-target="#assignModal{{ host.id }}">
                  ⚙️ Assigner
                </button>
              </td>
            </tr>
            {% endfor %}
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<!-- Modals pour assignation -->
{% for host in hosts %}
<div class="modal fade" id="assignModal{{ host.id }}" tabindex="-1">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title">Assigner groupe et template - {{ host.ip }}</h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
      </div>
      <form action="{{ url_for('assign_host_group_template', machine_id=host.id) }}" method="post">
        <div class="modal-body">
          <div class="mb-3">
            <label class="form-label">Groupe d'hôtes:</label>
            <select name="group_id" class="form-select">
              <option value="none">Aucun groupe</option>
              {% for group in groups %}
              <option value="{{ group.id }}" {% if host.host_group_id == group.id %}selected{% endif %}>{{ group.name }}</option>
              {% endfor %}
            </select>
          </div>
          <div class="mb-3">
            <label class="form-label">Template:</label>
            <select name="template_id" class="form-select">
              <option value="none">Aucun template</option>
              {% for template in templates %}
              <option value="{{ template.id }}" {% if host.template_id == template.id %}selected{% endif %}>{{ template.name }}</option>
              {% endfor %}
            </select>
          </div>
        </div>
        <div class="modal-footer">
          <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Annuler</button>
          <button type="submit" class="btn btn-primary">💾 Enregistrer</button>
        </div>
      </form>
    </div>
  </div>
</div>
{% endfor %}

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<script>
// Filtering functionality
document.addEventListener('DOMContentLoaded', function() {
  const groupFilter = document.getElementById('groupFilter');
  const templateFilter = document.getElementById('templateFilter');
  const statusFilter = document.getElementById('statusFilter');
  const clearFilters = document.getElementById('clearFilters');
  const table = document.getElementById('hostsTable');
  const rows = table.querySelectorAll('tbody tr');

  function filterTable() {
    const groupValue = groupFilter.value;
    const templateValue = templateFilter.value;
    const statusValue = statusFilter.value;

    rows.forEach(row => {
      const groupMatch = !groupValue || row.dataset.group === groupValue;
      const templateMatch = !templateValue || row.dataset.template === templateValue;
      const statusMatch = !statusValue || row.dataset.status === statusValue;

      if (groupMatch && templateMatch && statusMatch) {
        row.style.display = '';
      } else {
        row.style.display = 'none';
      }
    });
  }

  groupFilter.addEventListener('change', filterTable);
  templateFilter.addEventListener('change', filterTable);
  statusFilter.addEventListener('change', filterTable);

  clearFilters.addEventListener('click', function() {
    groupFilter.value = '';
    templateFilter.value = '';
    statusFilter.value = '';
    filterTable();
  });
});
</script>
{% endblock %}
