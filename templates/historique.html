{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2>📜 Historique des Scans</h2>
  <a href="{{ url_for('index') }}" class="btn btn-secondary mb-3">← Accueil</a>

  <div class="table-responsive">
    <table id="machinesTable" class="table table-bordered table-striped table-hover align-middle" style="width:100%">
      <thead class="table-dark">
        <tr>
          <th>IP</th>
          <th>Nom</th>
          <th>État</th>
          <th>Charge CPU (%)</th>
          <th>Date</th>
        </tr>
      </thead>
      <tbody>
        {% for machine in machines %}
        <tr>
          <td><a href="{{ url_for('machine_detail', ip=machine.ip) }}">{{ machine.ip }}</a></td>
          <td>{{ machine.name }}</td>
          <td>
            {% if machine.status == 'up' %}
              <span class="badge bg-success">En ligne</span>
            {% else %}
              <span class="badge bg-danger">Hors ligne</span>
            {% endif %}
          </td>
          <td>
            {% if machine.cpu_load is not none %}
              {% if machine.cpu_load < 50 %}
                <span class="badge bg-success">{{ machine.cpu_load }}%</span>
              {% elif machine.cpu_load < 80 %}
                <span class="badge bg-warning text-dark">{{ machine.cpu_load }}%</span>
              {% else %}
                <span class="badge bg-danger">{{ machine.cpu_load }}%</span>
              {% endif %}
            {% else %}
              <span class="text-muted">N/A</span>
            {% endif %}
          </td>
          <td>{{ machine.timestamp.strftime('%d/%m/%Y %H:%M:%S') }}</td>
        </tr>
        {% endfor %}
      </tbody>
    </table>
  </div>
</div>

<!-- Script pour initialiser DataTables -->
<script>
  $(document).ready(function () {
    $('#machinesTable').DataTable({
      language: {
        url: '//cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
      },
      order: [[4, 'desc']], // Trier par date décroissante par défaut
      pageLength: 10,
      lengthMenu: [5, 10, 25, 50]
    });
  });
</script>
{% endblock %}
