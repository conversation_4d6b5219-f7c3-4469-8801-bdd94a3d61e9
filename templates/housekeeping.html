{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2>🧹 Maintenance et Nettoyage (Housekeeping)</h2>
  <a href="{{ url_for('index') }}" class="btn btn-secondary mb-3">← Accueil</a>

  <!-- Statistiques de la base de données -->
  <div class="row mb-4">
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h5 class="card-title text-primary">{{ stats.total_machines }}</h5>
          <p class="card-text">Enregistrements machines</p>
          <small class="text-muted">{{ stats.old_machines }} anciens</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h5 class="card-title text-warning">{{ stats.total_alerts }}</h5>
          <p class="card-text">Alertes totales</p>
          <small class="text-muted">{{ stats.unacknowledged_alerts }} non acquittées</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h5 class="card-title text-info">{{ stats.total_history }}</h5>
          <p class="card-text">Historique items</p>
          <small class="text-muted">{{ stats.old_history }} anciens</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card text-center">
        <div class="card-body">
          <h5 class="card-title text-success">{{ stats.retention_days }}</h5>
          <p class="card-text">Jours de rétention</p>
          <small class="text-muted">Configuré</small>
        </div>
      </div>
    </div>
  </div>

  <!-- Informations sur la politique de rétention -->
  <div class="alert alert-info mb-4">
    <h6><i class="bi bi-info-circle"></i> Politique de rétention des données</h6>
    <p class="mb-2">
      <strong>Période de rétention:</strong> {{ stats.retention_days }} jours<br>
      <strong>Date limite:</strong> {{ stats.cutoff_date }}<br>
      <strong>Données concernées:</strong> Enregistrements de machines, historique des items, alertes acquittées
    </p>
    <p class="mb-0">
      <small>Les alertes non acquittées sont conservées indéfiniment jusqu'à leur traitement.</small>
    </p>
  </div>

  <!-- Actions de nettoyage -->
  <div class="row">
    <div class="col-md-4 mb-3">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">🗂️ Nettoyage des données anciennes</h6>
        </div>
        <div class="card-body">
          <p class="card-text">
            Supprime les enregistrements de machines et l'historique des items 
            plus anciens que {{ stats.retention_days }} jours.
          </p>
          <p class="text-muted">
            <strong>À supprimer:</strong> {{ stats.old_machines }} machines, {{ stats.old_history }} historiques
          </p>
          <button class="btn btn-warning" onclick="performCleanup('cleanup_old_data')">
            🗑️ Nettoyer les données
          </button>
        </div>
      </div>
    </div>

    <div class="col-md-4 mb-3">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">🔔 Nettoyage des alertes</h6>
        </div>
        <div class="card-body">
          <p class="card-text">
            Supprime les alertes acquittées plus anciennes que {{ stats.retention_days }} jours.
            Les alertes non acquittées sont conservées.
          </p>
          <p class="text-muted">
            <strong>À supprimer:</strong> {{ stats.old_acknowledged_alerts }} alertes acquittées
          </p>
          <button class="btn btn-warning" onclick="performCleanup('cleanup_alerts')">
            🗑️ Nettoyer les alertes
          </button>
        </div>
      </div>
    </div>

    <div class="col-md-4 mb-3">
      <div class="card">
        <div class="card-header">
          <h6 class="mb-0">📊 Nettoyage de l'historique</h6>
        </div>
        <div class="card-body">
          <p class="card-text">
            Supprime l'historique des métriques (items) plus ancien que {{ stats.retention_days }} jours.
          </p>
          <p class="text-muted">
            <strong>À supprimer:</strong> {{ stats.old_history }} enregistrements
          </p>
          <button class="btn btn-warning" onclick="performCleanup('cleanup_history')">
            🗑️ Nettoyer l'historique
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- Nettoyage complet -->
  <div class="card mb-4">
    <div class="card-header bg-danger text-white">
      <h6 class="mb-0">⚠️ Nettoyage complet</h6>
    </div>
    <div class="card-body">
      <p class="card-text">
        Effectue un nettoyage complet de toutes les données anciennes selon la politique de rétention.
        Cette action supprimera définitivement les données anciennes.
      </p>
      <div class="d-flex gap-2">
        <button class="btn btn-danger" onclick="performFullCleanup()">
          🧹 Nettoyage complet
        </button>
        <button class="btn btn-outline-primary" onclick="location.reload()">
          🔄 Actualiser les statistiques
        </button>
      </div>
    </div>
  </div>

  <!-- Résultats -->
  <div id="cleanupResults" class="mt-4"></div>

  <!-- Configuration automatique -->
  <div class="card">
    <div class="card-header">
      <h6 class="mb-0">⏰ Nettoyage automatique</h6>
    </div>
    <div class="card-body">
      <p class="card-text">
        Pour automatiser le nettoyage, vous pouvez configurer une tâche cron qui appelle l'endpoint d'API:
      </p>
      <div class="bg-light p-3 rounded">
        <code>
          # Nettoyage quotidien à 2h du matin<br>
          0 2 * * * curl -X POST http://localhost:5000/api/housekeeping/auto
        </code>
      </div>
      <p class="mt-2 mb-0">
        <small class="text-muted">
          Remplacez l'URL par l'adresse de votre serveur de monitoring.
        </small>
      </p>
    </div>
  </div>
</div>

<script>
function performCleanup(action) {
  const button = event.target;
  const originalText = button.innerHTML;
  
  button.disabled = true;
  button.innerHTML = '⏳ Nettoyage en cours...';
  
  const formData = new FormData();
  formData.append('action', action);
  
  fetch('/housekeeping', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    showResult(data.message, 'success');
    setTimeout(() => location.reload(), 2000);
  })
  .catch(error => {
    showResult('Erreur lors du nettoyage', 'danger');
  })
  .finally(() => {
    button.disabled = false;
    button.innerHTML = originalText;
  });
}

function performFullCleanup() {
  if (!confirm('Êtes-vous sûr de vouloir effectuer un nettoyage complet ? Cette action est irréversible.')) {
    return;
  }
  
  const actions = ['cleanup_old_data', 'cleanup_alerts', 'cleanup_history'];
  let completed = 0;
  
  showResult('Nettoyage complet en cours...', 'info');
  
  actions.forEach(action => {
    const formData = new FormData();
    formData.append('action', action);
    
    fetch('/housekeeping', {
      method: 'POST',
      body: formData
    })
    .then(response => response.json())
    .then(data => {
      completed++;
      if (completed === actions.length) {
        showResult('Nettoyage complet terminé avec succès', 'success');
        setTimeout(() => location.reload(), 2000);
      }
    })
    .catch(error => {
      showResult('Erreur lors du nettoyage complet', 'danger');
    });
  });
}

function showResult(message, type) {
  const resultsDiv = document.getElementById('cleanupResults');
  resultsDiv.innerHTML = `
    <div class="alert alert-${type} alert-dismissible fade show" role="alert">
      ${message}
      <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    </div>
  `;
}
</script>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}
