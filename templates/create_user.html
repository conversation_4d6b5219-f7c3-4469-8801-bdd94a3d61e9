{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2><i class="bi bi-person-plus"></i> Créer un nouvel utilisateur</h2>
  <a href="{{ url_for('manage_users') }}" class="btn btn-secondary mb-3">← Retour aux utilisateurs</a>

  <!-- Messages Flash -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          {{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card shadow-sm">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0"><i class="bi bi-person-badge"></i> Informations du nouvel utilisateur</h5>
        </div>
        <div class="card-body">
          <form method="POST">
            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="username" class="form-label">Nom d'utilisateur *</label>
                  <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-person"></i></span>
                    <input type="text" class="form-control" id="username" name="username" required>
                  </div>
                  <div class="form-text">Nom unique pour la connexion</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="email" class="form-label">Adresse email *</label>
                  <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-envelope"></i></span>
                    <input type="email" class="form-control" id="email" name="email" required>
                  </div>
                  <div class="form-text">Pour les notifications et récupération</div>
                </div>
              </div>
            </div>

            <div class="row">
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="password" class="form-label">Mot de passe *</label>
                  <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-lock"></i></span>
                    <input type="password" class="form-control" id="password" name="password" required>
                  </div>
                  <div class="form-text">Minimum 6 caractères</div>
                </div>
              </div>
              <div class="col-md-6">
                <div class="mb-3">
                  <label for="role" class="form-label">Rôle *</label>
                  <div class="input-group">
                    <span class="input-group-text"><i class="bi bi-shield"></i></span>
                    <select class="form-select" id="role" name="role" required>
                      <option value="">Choisir un rôle...</option>
                      <option value="viewer">Viewer - Lecture seule</option>
                      <option value="user">User - Lecture + Écriture</option>
                      <option value="admin">Admin - Tous droits</option>
                    </select>
                  </div>
                </div>
              </div>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
              <a href="{{ url_for('manage_users') }}" class="btn btn-outline-secondary me-md-2">
                <i class="bi bi-x-circle"></i> Annuler
              </a>
              <button type="submit" class="btn btn-success">
                <i class="bi bi-check-circle"></i> Créer l'utilisateur
              </button>
            </div>
          </form>
        </div>
      </div>

      <!-- Aide sur les rôles -->
      <div class="card mt-4">
        <div class="card-header">
          <h6 class="mb-0"><i class="bi bi-question-circle"></i> Guide des rôles</h6>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <div class="border rounded p-3 h-100">
                <h6 class="text-info"><i class="bi bi-eye"></i> Viewer</h6>
                <p class="small mb-2"><strong>Permissions :</strong></p>
                <ul class="small">
                  <li>Consulter les dashboards</li>
                  <li>Voir les alertes</li>
                  <li>Consulter l'historique</li>
                  <li>Voir les détails des machines</li>
                </ul>
                <p class="small text-muted mb-0"><strong>Idéal pour :</strong> Managers, clients, observateurs</p>
              </div>
            </div>
            <div class="col-md-4">
              <div class="border rounded p-3 h-100">
                <h6 class="text-success"><i class="bi bi-person-check"></i> User</h6>
                <p class="small mb-2"><strong>Permissions :</strong></p>
                <ul class="small">
                  <li>Toutes les permissions Viewer</li>
                  <li>Lancer des scans réseau</li>
                  <li>Acquitter les alertes</li>
                  <li>Créer/modifier des règles</li>
                  <li>Gérer groupes et templates</li>
                </ul>
                <p class="small text-muted mb-0"><strong>Idéal pour :</strong> Techniciens, opérateurs</p>
              </div>
            </div>
            <div class="col-md-4">
              <div class="border rounded p-3 h-100">
                <h6 class="text-danger"><i class="bi bi-shield-fill"></i> Admin</h6>
                <p class="small mb-2"><strong>Permissions :</strong></p>
                <ul class="small">
                  <li>Toutes les permissions User</li>
                  <li>Configuration système</li>
                  <li>Gestion des utilisateurs</li>
                  <li>Maintenance/nettoyage</li>
                  <li>Export/Import</li>
                </ul>
                <p class="small text-muted mb-0"><strong>Idéal pour :</strong> Administrateurs système</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
{% endblock %}
