{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2>✏️ Modifier le groupe "{{ group.name }}"</h2>
  <a href="{{ url_for('manage_hostgroups') }}" class="btn btn-secondary mb-3">← Retour aux groupes</a>

  <div class="card shadow-sm">
    <div class="card-body">
      <form method="POST">
        <div class="mb-3">
          <label for="name" class="form-label fw-semibold">Nom du groupe</label>
          <input
            type="text"
            class="form-control"
            id="name"
            name="name"
            value="{{ group.name }}"
            placeholder="Nom du groupe"
            required
          >
        </div>
        
        <div class="mb-4">
          <label for="description" class="form-label fw-semibold">Description</label>
          <textarea
            class="form-control"
            id="description"
            name="description"
            rows="3"
            placeholder="Description du groupe (optionnel)"
          >{{ group.description or '' }}</textarea>
        </div>

        <div class="mb-4">
          <h6>Informations:</h6>
          <ul class="list-unstyled">
            <li><strong>C<PERSON><PERSON> le:</strong> {{ group.created_at.strftime('%d/%m/%Y à %H:%M') }}</li>
            <li><strong>Nombre de machines:</strong> {{ group.machines|length }}</li>
          </ul>
        </div>

        {% if group.machines %}
        <div class="mb-4">
          <h6>Machines dans ce groupe:</h6>
          <div class="table-responsive">
            <table class="table table-sm table-striped">
              <thead>
                <tr>
                  <th>IP</th>
                  <th>Nom</th>
                  <th>Statut</th>
                  <th>Dernière mise à jour</th>
                </tr>
              </thead>
              <tbody>
                {% for machine in group.machines %}
                <tr>
                  <td><a href="{{ url_for('machine_detail', ip=machine.ip) }}">{{ machine.ip }}</a></td>
                  <td>{{ machine.name }}</td>
                  <td>
                    {% if machine.status == "up" %}
                      <span class="badge bg-success">En ligne</span>
                    {% else %}
                      <span class="badge bg-danger">Hors ligne</span>
                    {% endif %}
                  </td>
                  <td>{{ machine.timestamp.strftime('%d/%m/%Y %H:%M') }}</td>
                </tr>
                {% endfor %}
              </tbody>
            </table>
          </div>
        </div>
        {% endif %}

        <button type="submit" class="btn btn-primary me-2">💾 Enregistrer</button>
        <a href="{{ url_for('manage_hostgroups') }}" class="btn btn-outline-secondary">Annuler</a>
      </form>
    </div>
  </div>
</div>
{% endblock %}
