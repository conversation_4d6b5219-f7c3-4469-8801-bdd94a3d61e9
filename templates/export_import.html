{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2>📦 Export / Import de Configuration</h2>
  <a href="{{ url_for('index') }}" class="btn btn-secondary mb-3">← Accueil</a>

  <div class="row">
    <!-- Export Section -->
    <div class="col-md-6">
      <div class="card">
        <div class="card-header bg-primary text-white">
          <h5 class="mb-0">📤 Export de Configuration</h5>
        </div>
        <div class="card-body">
          <p class="card-text">
            Exportez votre configuration de monitoring pour la sauvegarder ou la transférer vers un autre système.
          </p>
          
          <div class="d-grid gap-2">
            <a href="{{ url_for('export_data', export_type='hostgroups') }}" class="btn btn-outline-primary">
              👥 Exporter les Groupes d'Hôtes
            </a>
            <a href="{{ url_for('export_data', export_type='templates') }}" class="btn btn-outline-primary">
              📋 Exporter les Templates
            </a>
            <a href="{{ url_for('export_data', export_type='rules') }}" class="btn btn-outline-primary">
              ⚙️ Exporter les Règles d'Alerte
            </a>
            <a href="{{ url_for('export_data', export_type='config') }}" class="btn btn-outline-primary">
              🔧 Exporter la Configuration Système
            </a>
            <hr>
            <a href="{{ url_for('export_data', export_type='all') }}" class="btn btn-primary">
              📦 Export Complet
            </a>
          </div>
          
          <div class="mt-3">
            <small class="text-muted">
              <strong>Note:</strong> Les exports sont au format JSON et incluent toutes les données de configuration.
              Les données sensibles comme les mots de passe sont incluses.
            </small>
          </div>
        </div>
      </div>
    </div>

    <!-- Import Section -->
    <div class="col-md-6">
      <div class="card">
        <div class="card-header bg-success text-white">
          <h5 class="mb-0">📥 Import de Configuration</h5>
        </div>
        <div class="card-body">
          <p class="card-text">
            Importez une configuration depuis un fichier JSON exporté précédemment.
          </p>
          
          <form id="importForm" enctype="multipart/form-data">
            <div class="mb-3">
              <label for="importFile" class="form-label">Fichier de configuration (JSON)</label>
              <input type="file" class="form-control" id="importFile" name="file" accept=".json" required>
              <div class="form-text">
                Sélectionnez un fichier JSON exporté depuis ce système ou un autre système compatible.
              </div>
            </div>
            
            <div class="mb-3">
              <div class="form-check">
                <input class="form-check-input" type="checkbox" id="confirmImport" required>
                <label class="form-check-label" for="confirmImport">
                  Je comprends que l'import peut modifier la configuration existante
                </label>
              </div>
            </div>
            
            <button type="submit" class="btn btn-success w-100">
              📥 Importer la Configuration
            </button>
          </form>
          
          <div id="importResult" class="mt-3"></div>
          
          <div class="mt-3">
            <small class="text-muted">
              <strong>Attention:</strong> L'import ne supprime pas les éléments existants, mais peut créer des doublons
              si les noms sont différents. Vérifiez votre configuration après l'import.
            </small>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Information Section -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">ℹ️ Informations sur l'Export/Import</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-6">
              <h6>Données exportées:</h6>
              <ul>
                <li><strong>Groupes d'hôtes:</strong> Noms, descriptions</li>
                <li><strong>Templates:</strong> Définitions, items, règles associées</li>
                <li><strong>Règles d'alerte:</strong> Conditions, messages, sévérités</li>
                <li><strong>Configuration:</strong> Paramètres SMTP, rétention, etc.</li>
              </ul>
            </div>
            <div class="col-md-6">
              <h6>Données NON exportées:</h6>
              <ul>
                <li>Historique des machines découvertes</li>
                <li>Alertes générées</li>
                <li>Historique des métriques</li>
                <li>Assignations hôtes ↔ groupes/templates</li>
              </ul>
            </div>
          </div>
          
          <div class="alert alert-warning mt-3">
            <h6><i class="bi bi-exclamation-triangle"></i> Recommandations de sécurité</h6>
            <ul class="mb-0">
              <li>Les fichiers d'export contiennent des informations sensibles (mots de passe SMTP)</li>
              <li>Stockez les exports dans un endroit sécurisé</li>
              <li>Ne partagez pas les fichiers d'export avec des personnes non autorisées</li>
              <li>Vérifiez la configuration après chaque import</li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Usage Examples -->
  <div class="row mt-4">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h5 class="mb-0">💡 Cas d'usage</h5>
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <h6>🔄 Migration de système</h6>
              <p class="small">
                Exportez la configuration complète d'un ancien système et importez-la sur un nouveau serveur.
              </p>
            </div>
            <div class="col-md-4">
              <h6>💾 Sauvegarde</h6>
              <p class="small">
                Créez des sauvegardes régulières de votre configuration pour pouvoir la restaurer en cas de problème.
              </p>
            </div>
            <div class="col-md-4">
              <h6>🔗 Synchronisation</h6>
              <p class="small">
                Partagez des templates et règles entre plusieurs instances de monitoring.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
document.getElementById('importForm').addEventListener('submit', function(e) {
  e.preventDefault();
  
  const formData = new FormData(this);
  const submitBtn = this.querySelector('button[type="submit"]');
  const resultDiv = document.getElementById('importResult');
  
  submitBtn.disabled = true;
  submitBtn.innerHTML = '⏳ Import en cours...';
  resultDiv.innerHTML = '';
  
  fetch('/import', {
    method: 'POST',
    body: formData
  })
  .then(response => response.json())
  .then(data => {
    if (data.success) {
      resultDiv.innerHTML = `
        <div class="alert alert-success">
          <i class="bi bi-check-circle"></i> ${data.message}
        </div>
      `;
      // Reset form
      document.getElementById('importFile').value = '';
      document.getElementById('confirmImport').checked = false;
    } else {
      resultDiv.innerHTML = `
        <div class="alert alert-danger">
          <i class="bi bi-exclamation-circle"></i> ${data.message}
        </div>
      `;
    }
  })
  .catch(error => {
    resultDiv.innerHTML = `
      <div class="alert alert-danger">
        <i class="bi bi-exclamation-circle"></i> Erreur de connexion
      </div>
    `;
  })
  .finally(() => {
    submitBtn.disabled = false;
    submitBtn.innerHTML = '📥 Importer la Configuration';
  });
});
</script>
{% endblock %}
