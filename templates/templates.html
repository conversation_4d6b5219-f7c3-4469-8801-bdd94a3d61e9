{% extends "base.html" %}

{% block content %}
<div class="container py-4">
  <h2>📋 Gestion des Templates</h2>
  <a href="{{ url_for('index') }}" class="btn btn-secondary mb-3">← Accueil</a>

  <!-- Formulaire d'ajout -->
  <div class="card mb-4">
    <div class="card-body">
      <h5 class="card-title">➕ Nouveau template</h5>
      <form method="POST">
        <div class="row mb-3">
          <div class="col-md-4">
            <label for="name" class="form-label">Nom du template</label>
            <input type="text" class="form-control" name="name" required>
          </div>
          <div class="col-md-6">
            <label for="description" class="form-label">Description</label>
            <input type="text" class="form-control" name="description" placeholder="Description optionnelle">
          </div>
          <div class="col-md-2 d-flex align-items-end">
            <button type="submit" class="btn btn-primary">💾 Créer</button>
          </div>
        </div>
      </form>
    </div>
  </div>

  <!-- Liste des templates -->
  <h3>📋 Templates existants</h3>
  <div class="row">
    {% for template in templates %}
    <div class="col-md-6 mb-3">
      <div class="card">
        <div class="card-body">
          <div class="d-flex justify-content-between align-items-start">
            <div>
              <h5 class="card-title">{{ template.name }}</h5>
              <p class="card-text text-muted">{{ template.description or 'Aucune description' }}</p>
              <small class="text-muted">
                Créé le {{ template.created_at.strftime('%d/%m/%Y') }} • 
                {{ template.machines|length }} machine(s) • 
                {{ template.items|length }} item(s) • 
                {{ template.alert_rules|length }} règle(s)
              </small>
            </div>
            <div class="dropdown">
              <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                Actions
              </button>
              <ul class="dropdown-menu">
                <li><a class="dropdown-item" href="{{ url_for('edit_template', template_id=template.id) }}">✏️ Modifier</a></li>
                <li><hr class="dropdown-divider"></li>
                <li>
                  <form action="{{ url_for('delete_template', template_id=template.id) }}" method="post" 
                        onsubmit="return confirm('Supprimer ce template ? Les machines ne seront pas supprimées.');" class="d-inline">
                    <button class="dropdown-item text-danger" type="submit">🗑 Supprimer</button>
                  </form>
                </li>
              </ul>
            </div>
          </div>
          
          {% if template.machines %}
          <div class="mt-3">
            <h6>Machines utilisant ce template:</h6>
            <div class="d-flex flex-wrap gap-1">
              {% for machine in template.machines[:5] %}
              <span class="badge bg-secondary">{{ machine.ip }}</span>
              {% endfor %}
              {% if template.machines|length > 5 %}
              <span class="badge bg-light text-dark">+{{ template.machines|length - 5 }} autres</span>
              {% endif %}
            </div>
          </div>
          {% endif %}
          
          {% if template.items %}
          <div class="mt-2">
            <h6>Items de monitoring:</h6>
            <div class="d-flex flex-wrap gap-1">
              {% for item in template.items[:3] %}
              <span class="badge bg-info">{{ item.name }}</span>
              {% endfor %}
              {% if template.items|length > 3 %}
              <span class="badge bg-light text-dark">+{{ template.items|length - 3 }} autres</span>
              {% endif %}
            </div>
          </div>
          {% endif %}
        </div>
      </div>
    </div>
    {% else %}
    <div class="col-12">
      <div class="alert alert-info">
        <i class="bi bi-info-circle"></i> Aucun template créé. Créez votre premier template ci-dessus.
      </div>
    </div>
    {% endfor %}
  </div>
</div>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
{% endblock %}
