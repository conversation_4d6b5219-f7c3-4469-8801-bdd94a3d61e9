{% extends "base.html" %}

{% block head %}
  {{ super() }}
  {# Ici aucune balise favicon #}
{% endblock %}

{% block content %}
<div class="container py-4">
  <h2>🔍 <PERSON><PERSON><PERSON> le r<PERSON>eau</h2>
  <a href="{{ url_for('dashboard') }}" class="btn btn-secondary mb-3">← Dashboard</a>

  <form method="post" class="mb-4 d-flex flex-wrap align-items-end gap-3">
    <div class="flex-grow-1">
      <label for="network_cidr" class="form-label">Plage IP à scanner</label>
      <input type="text" class="form-control" id="network_cidr" name="network_cidr" placeholder="Ex : ***********/24" value="{{ ip_input }}" required />
    </div>
    <button type="submit" class="btn btn-primary mt-3 mt-md-0">🔍 <PERSON><PERSON> le scan</button>
    <a href="{{ url_for('historique') }}" class="btn btn-outline-secondary mt-3 mt-md-0">📜 Voir l'historique</a>
  </form>

  {% if error %}
  <div class="alert alert-danger">{{ error }}</div>
  {% endif %}

  {% if alerts %}
  <div class="alert alert-warning">
    <h5>⚠️ Alertes :</h5>
    <ul>
      {% for alert in alerts %}
      <li>{{ alert }}</li>
      {% endfor %}
    </ul>
  </div>
  {% endif %}

  {% if results %}
  <div class="d-flex justify-content-between align-items-center mb-2">
    <div>
      <button id="filter-all" data-status="all" class="btn btn-outline-primary btn-sm btn-filter active">Tous</button>
      <button id="filter-up" data-status="up" class="btn btn-outline-success btn-sm btn-filter">En ligne</button>
      <button id="filter-down" data-status="down" class="btn btn-outline-danger btn-sm btn-filter">Hors ligne</button>
    </div>
    <div>
      <label>Afficher
        <select id="rowsCount" class="form-select form-select-sm d-inline-block w-auto ms-2">
          <option>5</option>
          <option selected>10</option>
          <option>25</option>
          <option>50</option>
          <option>100</option>
        </select>
        lignes
      </label>
    </div>
  </div>

  <table id="resultsTable" class="table table-bordered table-striped table-hover">
    <thead class="table-dark">
      <tr>
        <th>Adresse IP</th>
        <th>Nom (SNMP)</th>
        <th>État</th>
        <th>CPU (%)</th>
        <th>Mémoire (%)</th>
        <th>Disque (%)</th>
        <th>Uptime</th>
        <th>Description</th>
      </tr>
    </thead>
    <tbody>
      {% for machine in results %}
      <tr data-status="{{ machine['status'] if machine['status'] else machine.status }}">
        <td><a href="{{ url_for('machine_detail', ip=machine['ip'] if machine['ip'] else machine.ip) }}">{{ machine['ip'] if machine['ip'] else machine.ip }}</a></td>
        <td>{{ machine['name'] if machine['name'] else machine.name }}</td>
        <td>
          {% set status = machine['status'] if machine['status'] else machine.status %}
          {% if status == "up" %}
          <span class="badge bg-success">En ligne</span>
          {% else %}
          <span class="badge bg-danger">Hors ligne</span>
          {% endif %}
        </td>
        <td>
          {% set cpu = machine['cpu_load'] if machine['cpu_load'] else machine.cpu_load %}
          {% if cpu is not none %}
            <span class="badge {% if cpu > 80 %}bg-danger{% elif cpu > 60 %}bg-warning{% else %}bg-success{% endif %}">{{ cpu }}%</span>
          {% else %}
            <span class="text-muted">N/A</span>
          {% endif %}
        </td>
        <td>
          {% set memory = machine['memory_usage'] if machine['memory_usage'] else machine.memory_usage %}
          {% if memory is not none %}
            <span class="badge {% if memory > 80 %}bg-danger{% elif memory > 60 %}bg-warning{% else %}bg-success{% endif %}">{{ memory }}%</span>
          {% else %}
            <span class="text-muted">N/A</span>
          {% endif %}
        </td>
        <td>
          {% set disk = machine['disk_usage'] if machine['disk_usage'] else machine.disk_usage %}
          {% if disk is not none %}
            <span class="badge {% if disk > 80 %}bg-danger{% elif disk > 60 %}bg-warning{% else %}bg-success{% endif %}">{{ disk }}%</span>
          {% else %}
            <span class="text-muted">N/A</span>
          {% endif %}
        </td>
        <td>
          {% set uptime = machine['uptime'] if machine['uptime'] else machine.uptime %}
          {% if uptime is not none %}
            {% set days = (uptime // 86400) %}
            {% set hours = ((uptime % 86400) // 3600) %}
            {% set minutes = ((uptime % 3600) // 60) %}
            <small>{{ days }}d {{ hours }}h {{ minutes }}m</small>
          {% else %}
            <span class="text-muted">N/A</span>
          {% endif %}
        </td>
        <td>
          {% set desc = machine['description'] if machine['description'] else machine.description %}
          <small class="text-muted">{{ desc[:50] + '...' if desc and desc|length > 50 else desc if desc else 'N/A' }}</small>
        </td>
      </tr>
      {% endfor %}
    </tbody>
  </table>
  {% endif %}
</div>

<!-- CSS et JS DataTables + jQuery -->
<link href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css" rel="stylesheet" />
<script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
<script src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
<script>
  $(document).ready(function() {
    var table = $('#resultsTable').DataTable({
      language: {
        url: 'https://cdn.datatables.net/plug-ins/1.13.6/i18n/fr-FR.json'
      },
      pageLength: 10,
      lengthChange: false,
      dom: 'lrtip'  // Pas de recherche native, on fait filtres custom
    });

    $('#rowsCount').on('change', function() {
      table.page.len($(this).val()).draw();
    });

    // Filtre personnalisé DataTables selon le bouton actif
    $.fn.dataTable.ext.search.push(
      function(settings, data, dataIndex) {
        var selectedStatus = $('.btn-filter.active').data('status');
        var status = $('#resultsTable tbody tr').eq(dataIndex).data('status');
        if (selectedStatus === 'all') {
          return true; // Tout afficher
        }
        return status === selectedStatus;
      }
    );

    function updateFilter(btn) {
      $('.btn-filter').removeClass('active');
      $(btn).addClass('active');
      table.draw();
    }

    $('#filter-all').on('click', function() {
      updateFilter(this);
    });
    $('#filter-up').on('click', function() {
      updateFilter(this);
    });
    $('#filter-down').on('click', function() {
      updateFilter(this);
    });
  });
</script>
{% endblock %}
