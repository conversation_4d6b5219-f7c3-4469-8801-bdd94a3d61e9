{% extends "base.html" %}

{% block content %}

<!-- Heure locale style Zabbix modernisée -->
<div id="localTimeZabbix" style="
    position: fixed;
    top: 20px;
    right: 20px;
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%);
    color: #00ffff;
    font-family: 'Inter', monospace;
    font-weight: 700;
    padding: 12px 20px;
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 255, 255, 0.3);
    z-index: 9999;
    user-select: none;
    font-size: 14px;
    letter-spacing: 1px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 255, 255, 0.2);
">
  --:--:-- (UTC+1)
</div>

<div class="container py-4 fade-in-up">
  <!-- Messages Flash -->
  {% with messages = get_flashed_messages(with_categories=true) %}
    {% if messages %}
      {% for category, message in messages %}
        <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
          <i class="bi bi-info-circle me-2"></i>{{ message }}
          <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
      {% endfor %}
    {% endif %}
  {% endwith %}

  <!-- Header moderne du dashboard -->
  <div class="row mb-4">
    <div class="col-12">
      <div class="card" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none;">
        <div class="card-body text-center py-5">
          <h1 class="display-4 fw-bold mb-3">
            <i class="bi bi-speedometer2 me-3 pulse-animation"></i>Dashboard de Monitoring
          </h1>
          <p class="lead mb-4 opacity-90">Surveillance réseau en temps réel • Système professionnel</p>
          <div class="d-flex justify-content-center gap-3 flex-wrap">
            <button id="refreshBtn" class="btn btn-light btn-lg shadow">
              <i class="bi bi-arrow-clockwise me-2"></i>Actualiser
            </button>
            <div class="form-check form-switch d-flex align-items-center bg-white bg-opacity-10 rounded-pill px-3 py-2">
              <input class="form-check-input me-2" type="checkbox" id="autoRefresh" checked>
              <label class="form-check-label text-white fw-semibold" for="autoRefresh">
                <i class="bi bi-clock me-1"></i>Auto-refresh (30s)
              </label>
            </div>
            <a href="{{ url_for('scan_page') }}" class="btn btn-warning btn-lg shadow">
              <i class="bi bi-radar me-2"></i>Scanner Réseau
            </a>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Status bar -->
  <div class="alert alert-info d-flex justify-content-between align-items-center mb-4">
    <div>
      <span id="statusIndicator" class="badge bg-success">🟢 Système opérationnel</span>
      <span class="ms-3">Dernière mise à jour: <span id="lastUpdate">--:--:--</span></span>
    </div>
    <div>
      <small class="text-muted">Prochaine actualisation dans <span id="countdown">30</span>s</small>
    </div>
  </div>

  <!-- Statistiques principales -->
  <div class="row text-center g-4">
    <div class="col-md-3">
      <div class="card shadow-sm border-primary">
        <div class="card-body">
          <h6>Total machines</h6>
          <h2 class="text-primary" data-stat="total">{{ total_machines }}</h2>
        </div>
      </div>
    </div>

    <div class="col-md-3">
      <div class="card shadow-sm border-success">
        <div class="card-body">
          <h6>Machines en ligne</h6>
          <h2 class="text-success" data-stat="up">{{ up_count }}</h2>
        </div>
      </div>
    </div>

    <div class="col-md-3">
      <div class="card shadow-sm border-danger">
        <div class="card-body">
          <h6>Machines hors ligne</h6>
          <h2 class="text-danger" data-stat="down">{{ down_count }}</h2>
        </div>
      </div>
    </div>

    <div class="col-md-3">
      <div class="card shadow-sm border-warning">
        <div class="card-body">
          <h6>Alertes aujourd’hui</h6>
          <h2 class="text-warning" data-stat="alerts">{{ alert_count }}</h2>
        </div>
      </div>
    </div>
  </div>

  <hr class="my-4">

  <div class="row gy-4">
    <!-- Graphique répartition en ligne / hors ligne -->
    <div class="col-md-4">
      <div class="card shadow">
        <div class="card-body">
          <h5 class="text-center">Répartition des machines</h5>
          <canvas id="statusChart" width="400" height="400"
                  data-up="{{ up_count|default(0)|int }}"
                  data-down="{{ down_count|default(0)|int }}"></canvas>
        </div>
      </div>
    </div>

    <!-- Top hosts by CPU utilization -->
    <div class="col-md-8">
      <div class="card shadow">
        <div class="card-body">
          <h5 class="text-center">Top machines par utilisation CPU</h5>
          <table class="table table-striped table-hover mt-3">
            <thead>
              <tr>
                <th>Nom</th>
                <th>IP</th>
                <th>Charge CPU (%)</th>
                <th>État</th>
              </tr>
            </thead>
            <tbody>
              {% for host in top_cpu %}
              <tr>
                <td>{{ host.name }}</td>
                <td>{{ host.ip }}</td>
                <td>{{ host.cpu_load if host.cpu_load is not none else 'N/A' }}</td>
                <td>
                  {% if host.status == 'up' %}
                    <span class="badge bg-success">En ligne</span>
                  {% else %}
                    <span class="badge bg-danger">Hors ligne</span>
                  {% endif %}
                </td>
              </tr>
              {% else %}
              <tr><td colspan="4" class="text-center">Aucune donnée disponible</td></tr>
              {% endfor %}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </div>

  <hr class="my-4">

  <!-- Graphique évolution CPU dans le temps -->
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card shadow">
        <div class="card-body">
          <h5 class="text-center">Évolution de la charge CPU (dernières machines scannées)</h5>
          <canvas id="cpuChart"></canvas>
        </div>
      </div>
    </div>
  </div>

  <hr class="my-4">

  <!-- Alertes non acquittées -->
  <div class="row justify-content-center">
    <div class="col-md-8">
      <div class="card shadow">
        <div class="card-body">
          <h5 class="text-center">Alertes non acquittées</h5>
          {% if alerts %}
          <ul class="list-group">
            {% for alert in alerts %}
            <li class="list-group-item list-group-item-warning">
              <strong>{{ alert.timestamp.strftime('%d/%m/%Y %H:%M:%S') }}:</strong>
              {{ alert.message }}
            </li>
            {% endfor %}
          </ul>
          {% else %}
          <p class="text-center">Aucune alerte non acquittée</p>
          {% endif %}
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

<script>
  // Heure locale style Zabbix (heure et fuseau)
  function updateLocalTimeZabbix() {
    const now = new Date();
    const options = {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZoneName: 'short'
    };
    const formatted = now.toLocaleTimeString('fr-FR', options);
    document.getElementById('localTimeZabbix').textContent = formatted;
  }
  updateLocalTimeZabbix();
  setInterval(updateLocalTimeZabbix, 1000);

  // Auto-refresh functionality
  let autoRefreshInterval;
  let countdownInterval;
  let countdownValue = 30;

  function updateLastUpdateTime() {
    const now = new Date();
    const formatted = now.toLocaleString('fr-FR');
    document.getElementById('lastUpdate').textContent = formatted;
  }

  function startCountdown() {
    countdownValue = 30;
    countdownInterval = setInterval(() => {
      countdownValue--;
      document.getElementById('countdown').textContent = countdownValue;
      if (countdownValue <= 0) {
        clearInterval(countdownInterval);
        if (document.getElementById('autoRefresh').checked) {
          refreshDashboard();
        }
      }
    }, 1000);
  }

  function refreshDashboard() {
    // Show loading state
    document.getElementById('statusIndicator').innerHTML = '🟡 Actualisation...';
    document.getElementById('statusIndicator').className = 'badge bg-warning';

    // Fetch fresh data via AJAX
    fetch('/api/dashboard/stats')
      .then(response => response.json())
      .then(data => {
        // Update statistics
        document.querySelector('[data-stat="total"]').textContent = data.total_machines;
        document.querySelector('[data-stat="up"]').textContent = data.up_count;
        document.querySelector('[data-stat="down"]').textContent = data.down_count;
        document.querySelector('[data-stat="alerts"]').textContent = data.alert_count;

        // Update status indicator
        document.getElementById('statusIndicator').innerHTML = data.status_text;
        const statusClass = data.system_status === 'critical' ? 'bg-danger' :
                           data.system_status === 'warning' ? 'bg-warning' : 'bg-success';
        document.getElementById('statusIndicator').className = `badge ${statusClass}`;

        // Update last update time
        updateLastUpdateTime();

        // Reset countdown
        startCountdown();
      })
      .catch(error => {
        console.error('Error refreshing dashboard:', error);
        document.getElementById('statusIndicator').innerHTML = '🔴 Erreur de connexion';
        document.getElementById('statusIndicator').className = 'badge bg-danger';
      });
  }

  function startAutoRefresh() {
    if (autoRefreshInterval) clearInterval(autoRefreshInterval);
    if (countdownInterval) clearInterval(countdownInterval);

    if (document.getElementById('autoRefresh').checked) {
      startCountdown();
      autoRefreshInterval = setInterval(() => {
        refreshDashboard();
      }, 30000);
    }
  }

  // Event listeners
  document.getElementById('refreshBtn').addEventListener('click', refreshDashboard);
  document.getElementById('autoRefresh').addEventListener('change', startAutoRefresh);

  // Initialize
  updateLastUpdateTime();
  startAutoRefresh();

  // Graphique répartition up/down
  const canvas = document.getElementById('statusChart');
  const up = parseInt(canvas.dataset.up);
  const down = parseInt(canvas.dataset.down);
  const ctx = canvas.getContext('2d');

  new Chart(ctx, {
    type: 'doughnut',
    data: {
      labels: ['En ligne', 'Hors ligne'],
      datasets: [{
        data: [up, down],
        backgroundColor: ['#198754', '#dc3545'],
        hoverOffset: 20
      }]
    },
    options: {
      responsive: true,
      plugins: {
        legend: {
          position: 'bottom'
        }
      }
    }
  });

  // Graphique évolution CPU
  const cpuCtx = document.getElementById('cpuChart').getContext('2d');
  const cpuLabels = JSON.parse('{{ cpu_labels | safe }}');
  const cpuData = JSON.parse('{{ cpu_data | safe }}');

  new Chart(cpuCtx, {
    type: 'line',
    data: {
      labels: cpuLabels,
      datasets: [{
        label: 'Charge CPU (%)',
        data: cpuData,
        fill: false,
        borderColor: 'rgb(75, 192, 192)',
        tension: 0.1,
        pointRadius: 2
      }]
    },
    options: {
      scales: {
        x: { display: true, title: { display: true, text: 'Date / Heure' } },
        y: { display: true, title: { display: true, text: 'Charge CPU (%)' }, min: 0, max: 100 }
      }  
    }
  });
</script>
{% endblock %}
