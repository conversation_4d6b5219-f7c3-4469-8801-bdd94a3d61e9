#!/usr/bin/env python3
"""
Test de la nouvelle fonction send_email
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from app import app, Config
from datetime import datetime, timezone

def get_config_value(key, default=None):
    """Récupère une valeur de configuration depuis la base de données"""
    with app.app_context():
        config = Config.query.filter_by(key=key).first()
        return config.value if config else default

def send_email_new(subject, message):
    """Nouvelle version de send_email avec la logique corrigée"""
    try:
        # Get email configuration from database
        smtp_server = get_config_value('smtp_server')
        smtp_port = get_config_value('smtp_port', '587')
        smtp_user = get_config_value('smtp_user')
        smtp_password = get_config_value('smtp_password')
        email_from = get_config_value('email_from')

        # Check if configuration is complete
        if not smtp_server or not smtp_user or not smtp_password or not email_from:
            print("Configuration email incomplète - email non envoyé")
            return False

        # Check if values are not empty strings
        if not smtp_server.strip() or not smtp_user.strip() or not smtp_password.strip() or not email_from.strip():
            print("Configuration email incomplète - email non envoyé")
            return False

        msg = MIMEMultipart()
        msg['From'] = smtp_user  # Utiliser smtp_user comme expéditeur
        msg['To'] = smtp_user  # Send to admin user
        msg['Subject'] = subject
        msg.attach(MIMEText(message, 'plain'))

        server = smtplib.SMTP(smtp_server, int(smtp_port))
        server.starttls()
        server.login(smtp_user, smtp_password)
        server.sendmail(smtp_user, smtp_user, msg.as_string())  # Utiliser smtp_user comme expéditeur
        server.quit()
        print(f"Email envoyé : {subject}")
        return True
    except Exception as e:
        print(f"Erreur envoi email : {e}")
        return False

def test_new_email_function():
    """Test de la nouvelle fonction email"""
    
    print("🧪 Test de la Nouvelle Fonction Email")
    print("=" * 50)
    
    # Affichage de la configuration
    smtp_server = get_config_value('smtp_server')
    smtp_port = get_config_value('smtp_port')
    smtp_user = get_config_value('smtp_user')
    email_from = get_config_value('email_from')
    
    print(f"Serveur: {smtp_server}")
    print(f"Port: {smtp_port}")
    print(f"Utilisateur: {smtp_user}")
    print(f"Expéditeur: {email_from}")
    print("=" * 50)
    
    # Test d'envoi
    subject = "🧪 Test Nouvelle Fonction Email"
    message = f"""
Bonjour,

Test de la nouvelle fonction send_email avec la logique corrigée.

Détails:
- Date: {datetime.now(timezone.utc).strftime('%d/%m/%Y %H:%M:%S')} UTC
- Serveur: {smtp_server}
- Port: {smtp_port}
- Utilisateur: {smtp_user}
- Expéditeur utilisé: {smtp_user} (corrigé)

Si vous recevez cet email, la nouvelle fonction fonctionne !

Cordialement,
Système de Monitoring (Version Corrigée)
    """
    
    print("📤 Envoi de l'email de test...")
    result = send_email_new(subject, message)
    
    if result:
        print("✅ Email envoyé avec succès!")
        print("Vérifiez votre boîte email Gmail")
        return True
    else:
        print("❌ Échec de l'envoi")
        return False

if __name__ == "__main__":
    print("🔧 TEST NOUVELLE FONCTION EMAIL")
    print("=" * 60)
    
    success = test_new_email_function()
    
    if success:
        print("\n🎉 SUCCÈS!")
        print("La nouvelle fonction email fonctionne correctement")
    else:
        print("\n❌ ÉCHEC")
        print("Il y a encore un problème avec la fonction email")
