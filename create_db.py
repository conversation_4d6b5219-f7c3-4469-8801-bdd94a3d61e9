from app import app, db, HostGroup, Template, Config, User
from werkzeug.security import generate_password_hash

with app.app_context():
    print("Dropping all tables...")
    db.drop_all()
    print("Creating all tables...")
    db.create_all()

    # Create default host groups
    print("Creating default host groups...")
    default_groups = [
        HostGroup(name="Linux Servers", description="Linux-based servers"),
        HostGroup(name="Windows Servers", description="Windows-based servers"),
        HostGroup(name="Network Devices", description="Routers, switches, and network equipment"),
        HostGroup(name="Discovered Hosts", description="Automatically discovered hosts")
    ]
    for group in default_groups:
        db.session.add(group)

    # Create default templates
    print("Creating default templates...")
    default_templates = [
        Template(name="Linux Server Template", description="Standard monitoring for Linux servers"),
        Template(name="Windows Server Template", description="Standard monitoring for Windows servers"),
        Template(name="Network Device Template", description="Standard monitoring for network devices")
    ]
    for template in default_templates:
        db.session.add(template)

    # Create default users
    print("Creating default users...")
    default_users = [
        User(
            username="admin",
            email="<EMAIL>",
            password_hash=generate_password_hash("admin123"),
            role="admin"
        ),
        User(
            username="user",
            email="<EMAIL>",
            password_hash=generate_password_hash("user123"),
            role="user"
        ),
        User(
            username="viewer",
            email="<EMAIL>",
            password_hash=generate_password_hash("viewer123"),
            role="viewer"
        )
    ]
    for user in default_users:
        db.session.add(user)

    # Create default configuration
    print("Creating default configuration...")
    default_configs = [
        Config(key="smtp_server", value="smtp.gmail.com", description="SMTP server for email notifications"),
        Config(key="smtp_port", value="587", description="SMTP server port"),
        Config(key="smtp_user", value="", description="SMTP username"),
        Config(key="smtp_password", value="", description="SMTP password"),
        Config(key="email_from", value="<EMAIL>", description="From email address"),
        Config(key="scan_interval", value="300", description="Default scan interval in seconds"),
        Config(key="data_retention_days", value="30", description="Number of days to keep historical data"),
        Config(key="snmp_timeout", value="2", description="SNMP timeout in seconds"),
        Config(key="snmp_retries", value="1", description="Number of SNMP retries")
    ]
    for config in default_configs:
        db.session.add(config)

    db.session.commit()
    print("Database tables created successfully with default data.")
    print("Default users:")
    print("- Admin: admin / admin123")
    print("- User: user / user123")
    print("- Viewer: viewer / viewer123")