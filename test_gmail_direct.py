#!/usr/bin/env python3
"""
Test direct de la connexion Gmail
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

def test_gmail_direct():
    """Test direct avec les paramètres Gmail"""
    
    # Configuration
    smtp_server = "smtp.gmail.com"
    smtp_port = 587
    smtp_user = "<EMAIL>"
    smtp_password = "mxxq xxwx ejqi yfqj"
    
    print("🧪 Test Direct Gmail")
    print("=" * 50)
    print(f"Email: {smtp_user}")
    print(f"Mot de passe: {'*' * len(smtp_password)}")
    print("=" * 50)
    
    try:
        print("📡 Connexion au serveur Gmail...")
        server = smtplib.SMTP(smtp_server, smtp_port)
        print("✅ Connexion établie")
        
        print("🔐 Activation TLS...")
        server.starttls()
        print("✅ TLS activé")
        
        print("🔑 Test d'authentification...")
        server.login(smtp_user, smtp_password)
        print("✅ Authentification réussie!")
        
        print("📧 Préparation du message de test...")
        msg = MIMEMultipart()
        msg['From'] = smtp_user
        msg['To'] = smtp_user
        msg['Subject'] = "🧪 Test Direct Gmail - Monitoring"
        
        body = f"""
Bonjour,

Test direct de connexion Gmail réussi !

Détails:
- Serveur: {smtp_server}
- Port: {smtp_port}
- Email: {smtp_user}
- Date: {__import__('datetime').datetime.now()}

Ce test confirme que la configuration Gmail fonctionne.

Cordialement,
Test Direct
        """
        
        msg.attach(MIMEText(body, 'plain'))
        
        print("📤 Envoi de l'email...")
        server.sendmail(smtp_user, smtp_user, msg.as_string())
        print("✅ Email envoyé avec succès!")
        
        server.quit()
        print("🔌 Connexion fermée")
        
        print("\n🎉 TEST DIRECT RÉUSSI!")
        print("Vérifiez votre boîte email Gmail")
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ ERREUR D'AUTHENTIFICATION: {e}")
        print("\n🔧 Actions à effectuer:")
        print("1. Vérifiez que la 2FA est activée sur Gmail")
        print("2. Générez un NOUVEAU mot de passe d'application")
        print("3. Utilisez ce nouveau mot de passe")
        print("\n🔗 Lien: https://myaccount.google.com/apppasswords")
        return False
        
    except Exception as e:
        print(f"❌ ERREUR: {e}")
        return False

if __name__ == "__main__":
    print("🔍 TEST DIRECT GMAIL")
    print("=" * 60)
    
    success = test_gmail_direct()
    
    if not success:
        print("\n💡 SOLUTION:")
        print("1. Allez sur https://myaccount.google.com/apppasswords")
        print("2. Générez un nouveau mot de passe d'application")
        print("3. Remplacez le mot de passe dans la configuration")
        print("4. Relancez le test")
