#!/usr/bin/env python3
"""
Vérification du mot de passe dans la base de données
"""

from app import app, Config

def check_password_in_db():
    """Vérifie le mot de passe stocké dans la base de données"""
    
    with app.app_context():
        print("🔍 Vérification du Mot de Passe en Base")
        print("=" * 50)
        
        # Récupérer le mot de passe
        password_config = Config.query.filter_by(key='smtp_password').first()
        if not password_config:
            print("❌ Mot de passe non trouvé en base")
            return
        
        password = password_config.value
        
        print(f"Mot de passe stocké: '{password}'")
        print(f"Longueur: {len(password)}")
        print(f"Caractères: {[c for c in password]}")
        
        # Mot de passe attendu
        expected = "mxxq xxwx ejqi yfqj"
        print(f"\nMot de passe attendu: '{expected}'")
        print(f"Longueur attendue: {len(expected)}")
        
        if password == expected:
            print("✅ Mot de passe correct")
        else:
            print("❌ Mot de passe différent")
            print("Mise à jour nécessaire...")
            
            # Mise à jour
            password_config.value = expected
            from datetime import datetime, timezone
            password_config.updated_at = datetime.now(timezone.utc)
            
            try:
                from app import db
                db.session.commit()
                print("✅ Mot de passe mis à jour")
            except Exception as e:
                print(f"❌ Erreur mise à jour: {e}")

if __name__ == "__main__":
    check_password_in_db()
