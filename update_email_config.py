#!/usr/bin/env python3
"""
Script pour mettre à jour la configuration email dans la base de données
"""

from app import app, db, Config
from datetime import datetime, timezone

def update_email_config():
    """Met à jour la configuration email dans la base de données"""
    
    with app.app_context():
        print("🔧 Mise à jour de la Configuration Email")
        print("=" * 50)
        
        # Récupérer l'adresse email utilisateur
        smtp_user_config = Config.query.filter_by(key='smtp_user').first()
        if not smtp_user_config:
            print("❌ Configuration smtp_user introuvable")
            return False
        
        smtp_user = smtp_user_config.value
        
        # Mettre à jour email_from pour qu'il soit identique à smtp_user
        email_from_config = Config.query.filter_by(key='email_from').first()
        if not email_from_config:
            print("❌ Configuration email_from introuvable")
            return False
        
        old_value = email_from_config.value
        email_from_config.value = smtp_user
        email_from_config.updated_at = datetime.now(timezone.utc)
        
        try:
            db.session.commit()
            print(f"✅ email_from mis à jour: {old_value} → {smtp_user}")
            return True
        except Exception as e:
            db.session.rollback()
            print(f"❌ Erreur lors de la mise à jour: {e}")
            return False

if __name__ == "__main__":
    print("🔄 MISE À JOUR CONFIGURATION EMAIL")
    print("=" * 60)
    
    success = update_email_config()
    
    if success:
        print("\n🎉 CONFIGURATION MISE À JOUR AVEC SUCCÈS!")
        print("Redémarrez l'application pour appliquer les changements")
    else:
        print("\n❌ ÉCHEC DE LA MISE À JOUR")
        print("Vérifiez les erreurs ci-dessus")
