#!/usr/bin/env python3
"""
Script de test pour diagnostiquer les problèmes d'email
"""

import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
import ssl

def test_email_connection():
    """Test la connexion email avec diagnostic détaillé"""
    
    # Configuration
    smtp_server = "smtp.gmail.com"
    smtp_port = 587
    smtp_user = "<EMAIL>"
    smtp_password = "mxxq xxwx ejqi yfqj"
    email_from = "<EMAIL>"
    email_to = "<EMAIL>"
    
    print("🔍 Test de Configuration Email")
    print("=" * 50)
    print(f"Serveur SMTP: {smtp_server}")
    print(f"Port: {smtp_port}")
    print(f"Utilisateur: {smtp_user}")
    print(f"Mot de passe: {'*' * len(smtp_password)}")
    print(f"De: {email_from}")
    print(f"Vers: {email_to}")
    print("=" * 50)
    
    try:
        print("📡 Étape 1: Connexion au serveur SMTP...")
        server = smtplib.SMTP(smtp_server, smtp_port)
        print("✅ Connexion établie")
        
        print("🔐 Étape 2: Activation TLS...")
        server.starttls()
        print("✅ TLS activé")
        
        print("🔑 Étape 3: Authentification...")
        server.login(smtp_user, smtp_password)
        print("✅ Authentification réussie")
        
        print("📧 Étape 4: Préparation du message...")
        msg = MIMEMultipart()
        msg['From'] = email_from
        msg['To'] = email_to
        msg['Subject'] = "🧪 Test Email - Monitoring System"
        
        body = """
        Bonjour,
        
        Ceci est un email de test du système de monitoring.
        
        Si vous recevez cet email, la configuration fonctionne correctement !
        
        Détails du test:
        - Date: {date}
        - Serveur: {server}
        - Port: {port}
        
        Cordialement,
        Système de Monitoring
        """.format(
            date=__import__('datetime').datetime.now().strftime('%d/%m/%Y %H:%M:%S'),
            server=smtp_server,
            port=smtp_port
        )
        
        msg.attach(MIMEText(body, 'plain'))
        print("✅ Message préparé")
        
        print("📤 Étape 5: Envoi de l'email...")
        text = msg.as_string()
        server.sendmail(email_from, email_to, text)
        print("✅ Email envoyé avec succès!")
        
        print("🔌 Étape 6: Fermeture de la connexion...")
        server.quit()
        print("✅ Connexion fermée")
        
        print("\n🎉 TEST RÉUSSI!")
        print("Vérifiez votre boîte email (et le dossier spam)")
        return True
        
    except smtplib.SMTPAuthenticationError as e:
        print(f"❌ ERREUR D'AUTHENTIFICATION: {e}")
        print("\n🔧 Solutions possibles:")
        print("1. Vérifiez que la 2FA est activée sur Gmail")
        print("2. Générez un nouveau mot de passe d'application")
        print("3. Vérifiez que l'adresse email est correcte")
        return False
        
    except smtplib.SMTPConnectError as e:
        print(f"❌ ERREUR DE CONNEXION: {e}")
        print("\n🔧 Solutions possibles:")
        print("1. Vérifiez votre connexion internet")
        print("2. Vérifiez que le serveur SMTP est correct")
        print("3. Vérifiez que le port est correct")
        return False
        
    except smtplib.SMTPException as e:
        print(f"❌ ERREUR SMTP: {e}")
        return False
        
    except Exception as e:
        print(f"❌ ERREUR GÉNÉRALE: {e}")
        return False

def check_gmail_settings():
    """Vérifie les paramètres Gmail recommandés"""
    print("\n📋 Vérification des Paramètres Gmail")
    print("=" * 50)
    print("✅ Paramètres requis pour Gmail:")
    print("   - 2FA activée sur le compte Google")
    print("   - Mot de passe d'application généré")
    print("   - Serveur: smtp.gmail.com")
    print("   - Port: 587 (TLS)")
    print("\n🔗 Liens utiles:")
    print("   - 2FA: https://myaccount.google.com/security")
    print("   - Mots de passe d'app: https://myaccount.google.com/apppasswords")

if __name__ == "__main__":
    print("🧪 DIAGNOSTIC EMAIL - SYSTÈME DE MONITORING")
    print("=" * 60)
    
    check_gmail_settings()
    
    input("\nAppuyez sur Entrée pour lancer le test...")
    
    success = test_email_connection()
    
    if not success:
        print("\n🆘 AIDE SUPPLÉMENTAIRE:")
        print("1. Vérifiez que Gmail autorise les applications moins sécurisées")
        print("2. Essayez de vous connecter à Gmail depuis un navigateur")
        print("3. Vérifiez les logs de sécurité Google")
        print("4. Contactez l'administrateur si vous êtes en entreprise")
