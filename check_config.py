#!/usr/bin/env python3
"""
Script pour vérifier la configuration email dans la base de données
"""

from app import app, db, Config
from datetime import datetime, timezone

def check_email_config():
    """Vérifie la configuration email dans la base de données"""
    
    with app.app_context():
        print("🔍 Vérification de la Configuration Email")
        print("=" * 50)
        
        # Liste des clés de configuration email
        email_keys = ['smtp_server', 'smtp_port', 'smtp_user', 'smtp_password', 'email_from']
        
        config_found = {}
        
        for key in email_keys:
            config = Config.query.filter_by(key=key).first()
            if config:
                # Masquer le mot de passe pour l'affichage
                value = config.value
                if 'password' in key:
                    value = '*' * len(value) if value else 'VIDE'
                
                config_found[key] = {
                    'value': value,
                    'updated_at': config.updated_at
                }
                print(f"✅ {key}: {value}")
                if config.updated_at:
                    print(f"   Mis à jour: {config.updated_at.strftime('%d/%m/%Y %H:%M:%S')}")
            else:
                config_found[key] = None
                print(f"❌ {key}: NON CONFIGURÉ")
        
        print("\n" + "=" * 50)
        
        # Vérification de la complétude
        missing_configs = [key for key, value in config_found.items() if value is None]
        
        if missing_configs:
            print(f"⚠️  Configurations manquantes: {', '.join(missing_configs)}")
            return False
        else:
            print("✅ Toutes les configurations email sont présentes")
            return True

def test_email_from_app():
    """Test l'envoi d'email depuis l'application"""
    
    with app.app_context():
        print("\n📧 Test d'Envoi Email depuis l'Application")
        print("=" * 50)
        
        # Import des fonctions de l'app
        from app import send_email, get_config_value
        
        # Récupération de la configuration
        smtp_server = get_config_value('smtp_server')
        smtp_port = get_config_value('smtp_port')
        smtp_user = get_config_value('smtp_user')
        email_from = get_config_value('email_from')
        
        print(f"Serveur: {smtp_server}")
        print(f"Port: {smtp_port}")
        print(f"Utilisateur: {smtp_user}")
        print(f"Expéditeur: {email_from}")
        
        if not all([smtp_server, smtp_port, smtp_user, email_from]):
            print("❌ Configuration incomplète dans la base de données")
            return False
        
        # Test d'envoi
        print("\n📤 Tentative d'envoi...")
        
        subject = "🧪 Test Email - Système de Monitoring"
        message = f"""
Bonjour,

Ceci est un test d'email depuis l'application de monitoring.

Détails:
- Date: {datetime.now(timezone.utc).strftime('%d/%m/%Y %H:%M:%S')} UTC
- Serveur: {smtp_server}
- Port: {smtp_port}

Si vous recevez cet email, la configuration fonctionne !

Cordialement,
Système de Monitoring
        """
        
        try:
            result = send_email(subject, message)
            if result:
                print("✅ Email envoyé avec succès!")
                return True
            else:
                print("❌ Échec de l'envoi d'email")
                return False
        except Exception as e:
            print(f"❌ Erreur lors de l'envoi: {e}")
            return False

if __name__ == "__main__":
    print("🔧 DIAGNOSTIC CONFIGURATION EMAIL")
    print("=" * 60)
    
    # Vérification de la configuration
    config_ok = check_email_config()
    
    if config_ok:
        # Test d'envoi
        email_ok = test_email_from_app()
        
        if email_ok:
            print("\n🎉 TOUT FONCTIONNE CORRECTEMENT!")
            print("Vérifiez votre boîte email (et le dossier spam)")
        else:
            print("\n❌ PROBLÈME AVEC L'ENVOI D'EMAIL")
            print("La configuration est présente mais l'envoi échoue")
    else:
        print("\n❌ CONFIGURATION INCOMPLÈTE")
        print("Utilisez l'interface web pour configurer l'email")
        print("URL: http://127.0.0.1:5005/config")
