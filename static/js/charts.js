/* ===== GRAPHIQUES ANIMÉS MODERNES ===== */

// Configuration globale Chart.js
Chart.defaults.font.family = "'Inter', sans-serif";
Chart.defaults.font.size = 12;
Chart.defaults.color = '#4a5568';

// Couleurs du thème
const chartColors = {
    primary: '#667eea',
    secondary: '#764ba2',
    success: '#48bb78',
    warning: '#ed8936',
    danger: '#f56565',
    info: '#4299e1'
};

// Gradients
function createGradient(ctx, color1, color2) {
    const gradient = ctx.createLinearGradient(0, 0, 0, 400);
    gradient.addColorStop(0, color1);
    gradient.addColorStop(1, color2);
    return gradient;
}

// Graphique en temps réel pour le CPU
function createRealtimeChart(canvasId, label, color) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return null;

    const gradient = createGradient(ctx.getContext('2d'), color + '40', color + '10');

    return new Chart(ctx, {
        type: 'line',
        data: {
            labels: [],
            datasets: [{
                label: label,
                data: [],
                borderColor: color,
                backgroundColor: gradient,
                borderWidth: 3,
                fill: true,
                tension: 0.4,
                pointBackgroundColor: color,
                pointBorderColor: '#ffffff',
                pointBorderWidth: 2,
                pointRadius: 4,
                pointHoverRadius: 6
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    borderColor: color,
                    borderWidth: 1,
                    cornerRadius: 8,
                    displayColors: false
                }
            },
            scales: {
                x: {
                    display: true,
                    grid: {
                        color: 'rgba(0,0,0,0.05)'
                    },
                    ticks: {
                        maxTicksLimit: 10
                    }
                },
                y: {
                    display: true,
                    grid: {
                        color: 'rgba(0,0,0,0.05)'
                    },
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            },
            animation: {
                duration: 1000,
                easing: 'easeInOutQuart'
            },
            interaction: {
                intersect: false,
                mode: 'index'
            }
        }
    });
}

// Graphique en donut pour les statistiques
function createDonutChart(canvasId, data, labels, colors) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return null;

    return new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: data,
                backgroundColor: colors,
                borderWidth: 0,
                hoverBorderWidth: 3,
                hoverBorderColor: '#ffffff'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            cutout: '70%',
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true,
                        font: {
                            size: 12,
                            weight: '500'
                        }
                    }
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return `${context.label}: ${context.parsed} (${percentage}%)`;
                        }
                    }
                }
            },
            animation: {
                animateRotate: true,
                animateScale: true,
                duration: 1500,
                easing: 'easeInOutQuart'
            }
        }
    });
}

// Graphique en barres pour les top machines
function createBarChart(canvasId, data, labels, color) {
    const ctx = document.getElementById(canvasId);
    if (!ctx) return null;

    const gradient = createGradient(ctx.getContext('2d'), color, color + '80');

    return new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'Utilisation CPU',
                data: data,
                backgroundColor: gradient,
                borderColor: color,
                borderWidth: 2,
                borderRadius: 8,
                borderSkipped: false
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                },
                tooltip: {
                    backgroundColor: 'rgba(0,0,0,0.8)',
                    titleColor: '#ffffff',
                    bodyColor: '#ffffff',
                    cornerRadius: 8,
                    callbacks: {
                        label: function(context) {
                            return `CPU: ${context.parsed.y}%`;
                        }
                    }
                }
            },
            scales: {
                x: {
                    grid: {
                        display: false
                    },
                    ticks: {
                        maxRotation: 45
                    }
                },
                y: {
                    grid: {
                        color: 'rgba(0,0,0,0.05)'
                    },
                    beginAtZero: true,
                    max: 100,
                    ticks: {
                        callback: function(value) {
                            return value + '%';
                        }
                    }
                }
            },
            animation: {
                duration: 1200,
                easing: 'easeInOutQuart'
            }
        }
    });
}

// Mise à jour des données en temps réel
function updateRealtimeChart(chart, newValue, maxPoints = 20) {
    if (!chart) return;

    const now = new Date();
    const timeLabel = now.toLocaleTimeString();

    chart.data.labels.push(timeLabel);
    chart.data.datasets[0].data.push(newValue);

    // Limiter le nombre de points
    if (chart.data.labels.length > maxPoints) {
        chart.data.labels.shift();
        chart.data.datasets[0].data.shift();
    }

    chart.update('none'); // Animation fluide
}

// Animation des compteurs
function animateCounter(element, start, end, duration = 2000) {
    if (!element) return;

    const range = end - start;
    const increment = range / (duration / 16);
    let current = start;

    const timer = setInterval(() => {
        current += increment;
        if ((increment > 0 && current >= end) || (increment < 0 && current <= end)) {
            current = end;
            clearInterval(timer);
        }
        element.textContent = Math.floor(current);
    }, 16);
}

// Initialisation des graphiques au chargement de la page
document.addEventListener('DOMContentLoaded', function() {
    // Animer les compteurs de statistiques
    const statElements = document.querySelectorAll('[data-stat]');
    statElements.forEach(element => {
        const finalValue = parseInt(element.textContent);
        element.textContent = '0';
        setTimeout(() => {
            animateCounter(element, 0, finalValue);
        }, 500);
    });

    // Créer les graphiques si les éléments existent
    window.cpuChart = createRealtimeChart('cpuChart', 'CPU Usage', chartColors.danger);
    window.memoryChart = createRealtimeChart('memoryChart', 'Memory Usage', chartColors.warning);
    
    // Graphique de répartition des machines
    const machinesData = [
        parseInt(document.querySelector('[data-stat="up"]')?.textContent || 0),
        parseInt(document.querySelector('[data-stat="down"]')?.textContent || 0)
    ];
    
    if (machinesData[0] + machinesData[1] > 0) {
        window.machinesChart = createDonutChart(
            'machinesChart',
            machinesData,
            ['En ligne', 'Hors ligne'],
            [chartColors.success, chartColors.danger]
        );
    }
});

// Export des fonctions pour utilisation globale
window.chartUtils = {
    createRealtimeChart,
    createDonutChart,
    createBarChart,
    updateRealtimeChart,
    animateCounter,
    chartColors
};
