/* ===== SYSTÈME DE NOTIFICATIONS TOAST ===== */

class NotificationSystem {
    constructor() {
        this.container = this.createContainer();
        this.notifications = [];
    }

    createContainer() {
        const container = document.createElement('div');
        container.id = 'notification-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
            pointer-events: none;
        `;
        document.body.appendChild(container);
        return container;
    }

    show(message, type = 'info', duration = 5000) {
        const notification = this.createNotification(message, type);
        this.container.appendChild(notification);
        this.notifications.push(notification);

        // Animation d'entrée
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
            notification.style.opacity = '1';
        }, 100);

        // Auto-suppression
        setTimeout(() => {
            this.remove(notification);
        }, duration);

        return notification;
    }

    createNotification(message, type) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        
        const icons = {
            success: 'bi-check-circle-fill',
            error: 'bi-x-circle-fill',
            warning: 'bi-exclamation-triangle-fill',
            info: 'bi-info-circle-fill'
        };

        const colors = {
            success: 'linear-gradient(135deg, #48bb78 0%, #38a169 100%)',
            error: 'linear-gradient(135deg, #f56565 0%, #e53e3e 100%)',
            warning: 'linear-gradient(135deg, #ed8936 0%, #dd6b20 100%)',
            info: 'linear-gradient(135deg, #4299e1 0%, #3182ce 100%)'
        };

        notification.style.cssText = `
            background: ${colors[type] || colors.info};
            color: white;
            padding: 16px 20px;
            margin-bottom: 12px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.15);
            backdrop-filter: blur(10px);
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
            pointer-events: auto;
            cursor: pointer;
            display: flex;
            align-items: center;
            font-family: 'Inter', sans-serif;
            font-weight: 500;
            border: 1px solid rgba(255,255,255,0.2);
        `;

        notification.innerHTML = `
            <i class="bi ${icons[type] || icons.info} me-3" style="font-size: 1.2rem;"></i>
            <span style="flex: 1;">${message}</span>
            <i class="bi bi-x-lg ms-3" style="cursor: pointer; opacity: 0.7; font-size: 0.9rem;"></i>
        `;

        // Fermeture au clic
        notification.addEventListener('click', () => {
            this.remove(notification);
        });

        // Effet hover
        notification.addEventListener('mouseenter', () => {
            notification.style.transform = 'translateX(-8px) scale(1.02)';
        });

        notification.addEventListener('mouseleave', () => {
            notification.style.transform = 'translateX(0) scale(1)';
        });

        return notification;
    }

    remove(notification) {
        notification.style.transform = 'translateX(100%)';
        notification.style.opacity = '0';
        
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
            const index = this.notifications.indexOf(notification);
            if (index > -1) {
                this.notifications.splice(index, 1);
            }
        }, 300);
    }

    success(message, duration = 5000) {
        return this.show(message, 'success', duration);
    }

    error(message, duration = 7000) {
        return this.show(message, 'error', duration);
    }

    warning(message, duration = 6000) {
        return this.show(message, 'warning', duration);
    }

    info(message, duration = 5000) {
        return this.show(message, 'info', duration);
    }
}

// Instance globale
window.notifications = new NotificationSystem();

// Fonctions raccourcies globales
window.showSuccess = (message, duration) => notifications.success(message, duration);
window.showError = (message, duration) => notifications.error(message, duration);
window.showWarning = (message, duration) => notifications.warning(message, duration);
window.showInfo = (message, duration) => notifications.info(message, duration);

// Auto-conversion des messages Flash en notifications
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert');
    alerts.forEach(alert => {
        const message = alert.textContent.trim();
        let type = 'info';
        
        if (alert.classList.contains('alert-success')) type = 'success';
        else if (alert.classList.contains('alert-danger')) type = 'error';
        else if (alert.classList.contains('alert-warning')) type = 'warning';
        
        // Masquer l'alerte originale et afficher la notification
        alert.style.display = 'none';
        notifications.show(message, type);
    });
});

/* ===== SYSTÈME DE CONFIRMATION MODERNE ===== */

window.confirmAction = function(message, onConfirm, onCancel) {
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.5);
        backdrop-filter: blur(5px);
        z-index: 10001;
        display: flex;
        align-items: center;
        justify-content: center;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;

    const modal = document.createElement('div');
    modal.style.cssText = `
        background: white;
        border-radius: 16px;
        padding: 2rem;
        max-width: 400px;
        width: 90%;
        box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        transform: scale(0.9);
        transition: transform 0.3s ease;
        text-align: center;
    `;

    modal.innerHTML = `
        <div style="margin-bottom: 1.5rem;">
            <i class="bi bi-question-circle-fill text-warning" style="font-size: 3rem;"></i>
        </div>
        <h4 style="margin-bottom: 1rem; color: #2d3748;">Confirmation</h4>
        <p style="margin-bottom: 2rem; color: #4a5568;">${message}</p>
        <div style="display: flex; gap: 1rem; justify-content: center;">
            <button id="confirm-yes" class="btn btn-danger">
                <i class="bi bi-check-lg me-2"></i>Confirmer
            </button>
            <button id="confirm-no" class="btn btn-secondary">
                <i class="bi bi-x-lg me-2"></i>Annuler
            </button>
        </div>
    `;

    overlay.appendChild(modal);
    document.body.appendChild(overlay);

    // Animation d'entrée
    setTimeout(() => {
        overlay.style.opacity = '1';
        modal.style.transform = 'scale(1)';
    }, 10);

    // Gestion des clics
    document.getElementById('confirm-yes').addEventListener('click', () => {
        closeModal();
        if (onConfirm) onConfirm();
    });

    document.getElementById('confirm-no').addEventListener('click', () => {
        closeModal();
        if (onCancel) onCancel();
    });

    overlay.addEventListener('click', (e) => {
        if (e.target === overlay) {
            closeModal();
            if (onCancel) onCancel();
        }
    });

    function closeModal() {
        overlay.style.opacity = '0';
        modal.style.transform = 'scale(0.9)';
        setTimeout(() => {
            document.body.removeChild(overlay);
        }, 300);
    }
};

/* ===== LOADING SPINNER MODERNE ===== */

window.showLoading = function(message = 'Chargement...') {
    const overlay = document.createElement('div');
    overlay.id = 'loading-overlay';
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(255,255,255,0.9);
        backdrop-filter: blur(5px);
        z-index: 10002;
        display: flex;
        align-items: center;
        justify-content: center;
        flex-direction: column;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;

    overlay.innerHTML = `
        <div style="text-align: center;">
            <div class="spinner-border text-primary" style="width: 3rem; height: 3rem; margin-bottom: 1rem;" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p style="color: #4a5568; font-weight: 500; font-family: 'Inter', sans-serif;">${message}</p>
        </div>
    `;

    document.body.appendChild(overlay);
    setTimeout(() => overlay.style.opacity = '1', 10);
    
    return overlay;
};

window.hideLoading = function() {
    const overlay = document.getElementById('loading-overlay');
    if (overlay) {
        overlay.style.opacity = '0';
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 300);
    }
};
