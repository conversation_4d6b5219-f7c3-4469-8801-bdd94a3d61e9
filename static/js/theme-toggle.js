/* ===== SYSTÈME DE THÈME SOMBRE/CLAIR ===== */

class ThemeSystem {
    constructor() {
        this.currentTheme = localStorage.getItem('theme') || 'light';
        this.init();
    }

    init() {
        // Appliquer le thème sauvegardé
        this.applyTheme(this.currentTheme);
        
        // Créer le toggle
        this.createThemeToggle();
        
        // Écouter les changements de préférence système
        this.watchSystemTheme();
    }

    applyTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        this.currentTheme = theme;
        localStorage.setItem('theme', theme);
        
        // Mettre à jour les méta-tags pour mobile
        this.updateMetaTheme(theme);
    }

    updateMetaTheme(theme) {
        const metaTheme = document.querySelector('meta[name="theme-color"]');
        const color = theme === 'dark' ? '#1a202c' : '#667eea';
        
        if (metaTheme) {
            metaTheme.setAttribute('content', color);
        } else {
            const meta = document.createElement('meta');
            meta.name = 'theme-color';
            meta.content = color;
            document.head.appendChild(meta);
        }
    }

    toggle() {
        const newTheme = this.currentTheme === 'light' ? 'dark' : 'light';
        this.applyTheme(newTheme);
        
        // Animation de transition
        this.animateThemeChange();
        
        // Son si activé
        if (window.soundSystem) {
            soundSystem.play('click');
        }
        
        // Notification
        if (window.notifications) {
            notifications.info(
                `Thème ${newTheme === 'dark' ? 'sombre' : 'clair'} activé`,
                2000
            );
        }
        
        return newTheme;
    }

    animateThemeChange() {
        document.body.style.transition = 'all 0.3s ease';
        setTimeout(() => {
            document.body.style.transition = '';
        }, 300);
    }

    createThemeToggle() {
        const toggle = document.createElement('div');
        toggle.id = 'theme-toggle';
        toggle.style.cssText = `
            position: fixed;
            bottom: 80px;
            right: 20px;
            z-index: 1000;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 12px;
            border-radius: 50px;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
        `;
        
        this.updateToggleIcon(toggle);
        
        toggle.addEventListener('click', () => {
            this.toggle();
            this.updateToggleIcon(toggle);
        });
        
        toggle.addEventListener('mouseenter', () => {
            toggle.style.transform = 'scale(1.1)';
            toggle.style.background = 'rgba(102, 126, 234, 0.9)';
        });
        
        toggle.addEventListener('mouseleave', () => {
            toggle.style.transform = 'scale(1)';
            toggle.style.background = 'rgba(0,0,0,0.8)';
        });
        
        document.body.appendChild(toggle);
        this.toggleElement = toggle;
    }

    updateToggleIcon(toggle) {
        const icon = this.currentTheme === 'light' ? 'bi-moon-fill' : 'bi-sun-fill';
        const title = this.currentTheme === 'light' ? 'Activer le thème sombre' : 'Activer le thème clair';
        
        toggle.innerHTML = `<i class="bi ${icon}"></i>`;
        toggle.title = title;
    }

    watchSystemTheme() {
        if (window.matchMedia) {
            const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
            
            mediaQuery.addEventListener('change', (e) => {
                // Seulement si l'utilisateur n'a pas défini de préférence
                if (!localStorage.getItem('theme')) {
                    this.applyTheme(e.matches ? 'dark' : 'light');
                    this.updateToggleIcon(this.toggleElement);
                }
            });
        }
    }

    getCurrentTheme() {
        return this.currentTheme;
    }

    isDark() {
        return this.currentTheme === 'dark';
    }
}

// CSS pour le thème sombre
const darkThemeCSS = `
[data-theme="dark"] {
    --bg-primary: #1a202c;
    --bg-secondary: #2d3748;
    --text-primary: #f7fafc;
    --text-secondary: #e2e8f0;
    --border-color: rgba(255,255,255,0.1);
}

[data-theme="dark"] body {
    background: linear-gradient(135deg, #1a202c 0%, #2d3748 100%) !important;
    color: var(--text-primary);
}

[data-theme="dark"] #content {
    background: rgba(26, 32, 44, 0.95) !important;
    color: var(--text-primary);
}

[data-theme="dark"] .card {
    background: rgba(45, 55, 72, 0.9) !important;
    color: var(--text-primary);
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .card-body {
    color: var(--text-primary);
}

[data-theme="dark"] .table {
    color: var(--text-primary);
}

[data-theme="dark"] .table tbody tr {
    background: transparent;
}

[data-theme="dark"] .table tbody tr:hover {
    background: rgba(102, 126, 234, 0.1) !important;
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background: rgba(45, 55, 72, 0.8) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background: rgba(45, 55, 72, 1) !important;
    border-color: var(--primary-color) !important;
}

[data-theme="dark"] .input-group-text {
    background: rgba(45, 55, 72, 0.8) !important;
    color: var(--text-primary) !important;
    border-color: var(--border-color) !important;
}

[data-theme="dark"] .text-muted {
    color: var(--text-secondary) !important;
}

[data-theme="dark"] h1, [data-theme="dark"] h2, [data-theme="dark"] h3, 
[data-theme="dark"] h4, [data-theme="dark"] h5, [data-theme="dark"] h6 {
    color: var(--text-primary) !important;
}

[data-theme="dark"] .alert {
    border: 1px solid var(--border-color) !important;
}

[data-theme="dark"] .modal-content {
    background: var(--bg-secondary) !important;
    color: var(--text-primary) !important;
}

/* Animations pour la transition de thème */
[data-theme="dark"] * {
    transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}
`;

// Ajouter le CSS du thème sombre
const styleSheet = document.createElement('style');
styleSheet.textContent = darkThemeCSS;
document.head.appendChild(styleSheet);

// Instance globale
window.themeSystem = new ThemeSystem();

// Fonctions raccourcies
window.toggleTheme = () => themeSystem.toggle();
window.getCurrentTheme = () => themeSystem.getCurrentTheme();
window.isDarkTheme = () => themeSystem.isDark();
