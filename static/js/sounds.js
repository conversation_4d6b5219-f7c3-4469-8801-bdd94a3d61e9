/* ===== SYSTÈME DE SONS D'INTERFACE ===== */

class SoundSystem {
    constructor() {
        this.enabled = localStorage.getItem('soundsEnabled') !== 'false';
        this.volume = parseFloat(localStorage.getItem('soundVolume')) || 0.3;
        this.sounds = {};
        this.initSounds();
        this.bindEvents();
    }

    initSounds() {
        // Création des sons avec Web Audio API
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        
        // Sons générés procéduralement
        this.sounds = {
            click: this.createTone(800, 0.1, 'sine'),
            hover: this.createTone(600, 0.05, 'sine'),
            success: this.createChord([523, 659, 784], 0.3), // Do, Mi, Sol
            error: this.createTone(200, 0.4, 'sawtooth'),
            notification: this.createChord([440, 554, 659], 0.2), // La, Do#, Mi
            scan: this.createSweep(400, 800, 1.0),
            refresh: this.createTone(1000, 0.15, 'triangle')
        };
    }

    createTone(frequency, duration, type = 'sine') {
        return () => {
            if (!this.enabled) return;
            
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
            oscillator.type = type;
            
            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(this.volume, this.audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + duration);
        };
    }

    createChord(frequencies, duration) {
        return () => {
            if (!this.enabled) return;
            
            frequencies.forEach((freq, index) => {
                setTimeout(() => {
                    this.createTone(freq, duration * 0.8)();
                }, index * 50);
            });
        };
    }

    createSweep(startFreq, endFreq, duration) {
        return () => {
            if (!this.enabled) return;
            
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.connect(gainNode);
            gainNode.connect(this.audioContext.destination);
            
            oscillator.frequency.setValueAtTime(startFreq, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(endFreq, this.audioContext.currentTime + duration);
            
            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(this.volume * 0.5, this.audioContext.currentTime + 0.1);
            gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + duration);
        };
    }

    bindEvents() {
        document.addEventListener('DOMContentLoaded', () => {
            // Sons pour les boutons
            document.addEventListener('click', (e) => {
                if (e.target.matches('.btn')) {
                    if (e.target.classList.contains('btn-success')) {
                        this.play('success');
                    } else if (e.target.classList.contains('btn-danger')) {
                        this.play('error');
                    } else {
                        this.play('click');
                    }
                }
            });

            // Sons pour les liens de navigation
            document.addEventListener('click', (e) => {
                if (e.target.matches('.nav-link')) {
                    this.play('click');
                }
            });

            // Sons pour les hover sur les cartes
            document.addEventListener('mouseenter', (e) => {
                if (e.target.matches('.card')) {
                    this.play('hover');
                }
            }, true);

            // Son pour le refresh
            document.addEventListener('click', (e) => {
                if (e.target.matches('#refreshBtn') || e.target.closest('#refreshBtn')) {
                    this.play('refresh');
                }
            });
        });
    }

    play(soundName) {
        if (this.sounds[soundName] && this.enabled) {
            try {
                this.sounds[soundName]();
            } catch (error) {
                console.warn('Erreur lors de la lecture du son:', error);
            }
        }
    }

    toggle() {
        this.enabled = !this.enabled;
        localStorage.setItem('soundsEnabled', this.enabled);
        return this.enabled;
    }

    setVolume(volume) {
        this.volume = Math.max(0, Math.min(1, volume));
        localStorage.setItem('soundVolume', this.volume);
    }

    isEnabled() {
        return this.enabled;
    }
}

// Instance globale
window.soundSystem = new SoundSystem();

// Fonctions raccourcies
window.playSound = (soundName) => soundSystem.play(soundName);
window.toggleSounds = () => soundSystem.toggle();

/* ===== CONTRÔLES AUDIO DANS L'INTERFACE ===== */

// Ajouter un toggle pour les sons dans la navigation
document.addEventListener('DOMContentLoaded', function() {
    // Créer le contrôle des sons
    const soundControl = document.createElement('div');
    soundControl.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        z-index: 1000;
        background: rgba(0,0,0,0.8);
        color: white;
        padding: 12px;
        border-radius: 50px;
        cursor: pointer;
        transition: all 0.3s ease;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.2);
    `;
    
    function updateSoundIcon() {
        soundControl.innerHTML = soundSystem.isEnabled() 
            ? '<i class="bi bi-volume-up-fill"></i>' 
            : '<i class="bi bi-volume-mute-fill"></i>';
        soundControl.title = soundSystem.isEnabled() ? 'Désactiver les sons' : 'Activer les sons';
    }
    
    updateSoundIcon();
    
    soundControl.addEventListener('click', () => {
        const enabled = soundSystem.toggle();
        updateSoundIcon();
        
        // Notification
        if (window.notifications) {
            notifications.info(
                enabled ? 'Sons activés' : 'Sons désactivés',
                2000
            );
        }
        
        // Son de test si activé
        if (enabled) {
            setTimeout(() => soundSystem.play('success'), 100);
        }
    });
    
    soundControl.addEventListener('mouseenter', () => {
        soundControl.style.transform = 'scale(1.1)';
        soundControl.style.background = 'rgba(102, 126, 234, 0.9)';
    });
    
    soundControl.addEventListener('mouseleave', () => {
        soundControl.style.transform = 'scale(1)';
        soundControl.style.background = 'rgba(0,0,0,0.8)';
    });
    
    document.body.appendChild(soundControl);
});
