/* ===== SYSTÈME D'AUTO-REFRESH INTELLIGENT ===== */

class AutoRefreshSystem {
    constructor() {
        this.intervals = new Map();
        this.isVisible = true;
        this.defaultInterval = 30000; // 30 secondes
        this.fastInterval = 5000; // 5 secondes pour les alertes
        this.slowInterval = 60000; // 1 minute quand inactif
        
        this.initVisibilityDetection();
        this.initNetworkDetection();
    }

    initVisibilityDetection() {
        // Détecter si l'onglet est visible
        document.addEventListener('visibilitychange', () => {
            this.isVisible = !document.hidden;
            this.adjustRefreshRates();
        });

        // Détecter l'activité utilisateur
        let lastActivity = Date.now();
        const updateActivity = () => {
            lastActivity = Date.now();
        };

        ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'].forEach(event => {
            document.addEventListener(event, updateActivity, true);
        });

        // Vérifier l'inactivité toutes les minutes
        setInterval(() => {
            const inactive = Date.now() - lastActivity > 300000; // 5 minutes
            this.adjustRefreshRates(inactive);
        }, 60000);
    }

    initNetworkDetection() {
        // Détecter les problèmes de réseau
        window.addEventListener('online', () => {
            this.showNetworkStatus('Connexion rétablie', 'success');
            this.resumeAll();
        });

        window.addEventListener('offline', () => {
            this.showNetworkStatus('Connexion perdue', 'warning');
            this.pauseAll();
        });
    }

    adjustRefreshRates(inactive = false) {
        const multiplier = this.isVisible && !inactive ? 1 : 2;
        
        this.intervals.forEach((config, key) => {
            if (config.timer) {
                clearInterval(config.timer);
                config.timer = setInterval(config.callback, config.interval * multiplier);
            }
        });
    }

    register(key, callback, interval = this.defaultInterval, options = {}) {
        // Arrêter l'ancien timer s'il existe
        if (this.intervals.has(key)) {
            this.stop(key);
        }

        const config = {
            callback,
            interval,
            options,
            timer: null,
            lastRun: 0,
            errors: 0,
            maxErrors: options.maxErrors || 3
        };

        // Wrapper pour gérer les erreurs
        const wrappedCallback = async () => {
            try {
                config.lastRun = Date.now();
                
                // Indicateur de chargement
                if (options.showLoading) {
                    this.showRefreshIndicator(key);
                }

                await callback();
                config.errors = 0; // Reset des erreurs en cas de succès
                
                // Son de succès si activé
                if (options.playSound && window.soundSystem) {
                    soundSystem.play('refresh');
                }

            } catch (error) {
                config.errors++;
                console.warn(`Erreur lors du refresh ${key}:`, error);
                
                // Arrêter après trop d'erreurs
                if (config.errors >= config.maxErrors) {
                    this.stop(key);
                    this.showError(`Auto-refresh ${key} arrêté après ${config.maxErrors} erreurs`);
                }
            } finally {
                if (options.showLoading) {
                    this.hideRefreshIndicator(key);
                }
            }
        };

        config.timer = setInterval(wrappedCallback, interval);
        this.intervals.set(key, config);

        // Exécution immédiate si demandée
        if (options.immediate) {
            wrappedCallback();
        }

        return key;
    }

    stop(key) {
        const config = this.intervals.get(key);
        if (config && config.timer) {
            clearInterval(config.timer);
            config.timer = null;
        }
        this.intervals.delete(key);
    }

    pause(key) {
        const config = this.intervals.get(key);
        if (config && config.timer) {
            clearInterval(config.timer);
            config.timer = null;
        }
    }

    resume(key) {
        const config = this.intervals.get(key);
        if (config && !config.timer) {
            config.timer = setInterval(config.callback, config.interval);
        }
    }

    pauseAll() {
        this.intervals.forEach((config, key) => {
            this.pause(key);
        });
    }

    resumeAll() {
        this.intervals.forEach((config, key) => {
            this.resume(key);
        });
    }

    stopAll() {
        this.intervals.forEach((config, key) => {
            this.stop(key);
        });
    }

    getStatus() {
        const status = {};
        this.intervals.forEach((config, key) => {
            status[key] = {
                active: !!config.timer,
                interval: config.interval,
                lastRun: config.lastRun,
                errors: config.errors
            };
        });
        return status;
    }

    showRefreshIndicator(key) {
        const indicator = document.getElementById(`refresh-${key}`) || this.createRefreshIndicator(key);
        indicator.style.display = 'block';
        indicator.classList.add('spinning');
    }

    hideRefreshIndicator(key) {
        const indicator = document.getElementById(`refresh-${key}`);
        if (indicator) {
            indicator.style.display = 'none';
            indicator.classList.remove('spinning');
        }
    }

    createRefreshIndicator(key) {
        const indicator = document.createElement('div');
        indicator.id = `refresh-${key}`;
        indicator.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            background: rgba(102, 126, 234, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            z-index: 9998;
            display: none;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255,255,255,0.2);
        `;
        indicator.innerHTML = `<i class="bi bi-arrow-clockwise me-1"></i>Actualisation ${key}...`;
        document.body.appendChild(indicator);
        return indicator;
    }

    showNetworkStatus(message, type) {
        if (window.notifications) {
            notifications.show(message, type, 3000);
        }
    }

    showError(message) {
        if (window.notifications) {
            notifications.error(message);
        }
    }
}

// Instance globale
window.autoRefresh = new AutoRefreshSystem();

// CSS pour l'animation de rotation
const style = document.createElement('style');
style.textContent = `
    .spinning {
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        from { transform: rotate(0deg); }
        to { transform: rotate(360deg); }
    }
`;
document.head.appendChild(style);

// Fonctions raccourcies
window.registerRefresh = (key, callback, interval, options) => 
    autoRefresh.register(key, callback, interval, options);

window.stopRefresh = (key) => autoRefresh.stop(key);

// Auto-initialisation pour le dashboard
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh pour les statistiques du dashboard
    if (window.location.pathname === '/' || window.location.pathname.includes('dashboard')) {
        registerRefresh('stats', async () => {
            // Simuler la mise à jour des statistiques
            const response = await fetch('/api/stats');
            if (response.ok) {
                const data = await response.json();
                updateDashboardStats(data);
            }
        }, 30000, {
            showLoading: true,
            playSound: false,
            immediate: false
        });
    }

    // Auto-refresh pour les alertes
    if (window.location.pathname.includes('alerts')) {
        registerRefresh('alerts', async () => {
            const response = await fetch('/api/alerts');
            if (response.ok) {
                const data = await response.json();
                updateAlertsTable(data);
            }
        }, 15000, {
            showLoading: true,
            playSound: true,
            immediate: false
        });
    }
});

// Fonction pour mettre à jour les stats du dashboard
function updateDashboardStats(data) {
    const elements = {
        total: document.querySelector('[data-stat="total"]'),
        up: document.querySelector('[data-stat="up"]'),
        down: document.querySelector('[data-stat="down"]'),
        alerts: document.querySelector('[data-stat="alerts"]')
    };

    Object.keys(elements).forEach(key => {
        if (elements[key] && data[key] !== undefined) {
            const oldValue = parseInt(elements[key].textContent);
            const newValue = data[key];
            
            if (oldValue !== newValue) {
                // Animation du changement
                elements[key].style.transform = 'scale(1.1)';
                elements[key].style.color = '#667eea';
                
                setTimeout(() => {
                    if (window.chartUtils) {
                        chartUtils.animateCounter(elements[key], oldValue, newValue, 1000);
                    } else {
                        elements[key].textContent = newValue;
                    }
                    
                    setTimeout(() => {
                        elements[key].style.transform = 'scale(1)';
                        elements[key].style.color = '';
                    }, 1000);
                }, 100);
            }
        }
    });
}

// Fonction pour mettre à jour le tableau des alertes
function updateAlertsTable(data) {
    // Cette fonction sera implémentée selon la structure de votre tableau d'alertes
    console.log('Mise à jour des alertes:', data);
}
