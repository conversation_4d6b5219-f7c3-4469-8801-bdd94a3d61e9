/* ===== THÈME AMÉLIORÉ MONITORING SYSTEM ===== */
/* Amélioration de toutes les pages SAUF login */

:root {
  /* Couleurs principales */
  --primary-color: #667eea;
  --primary-dark: #5a67d8;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --success-color: #48bb78;
  --warning-color: #ed8936;
  --danger-color: #f56565;
  --info-color: #4299e1;

  /* Variables pour mode sombre/clair */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --text-primary: #2d3748;
  --text-secondary: #4a5568;
  --border-color: rgba(0,0,0,0.1);
}

/* Mode sombre */
[data-theme="dark"] {
  --bg-primary: #1a202c;
  --bg-secondary: #2d3748;
  --text-primary: #f7fafc;
  --text-secondary: #e2e8f0;
  --border-color: rgba(255,255,255,0.1);
}
  
  /* Couleurs neutres */
  --dark-bg: #1a202c;
  --dark-surface: #2d3748;
  --dark-border: #4a5568;
  --light-text: #f7fafc;
  --muted-text: #a0aec0;
  
  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
  --gradient-success: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
  --gradient-warning: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
  --gradient-danger: linear-gradient(135deg, #f56565 0%, #e53e3e 100%);
  
  /* Ombres */
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1), 0 2px 4px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15), 0 10px 10px rgba(0, 0, 0, 0.04);
  
  /* Animations */
  --transition-fast: all 0.2s ease;
  --transition-normal: all 0.3s ease;
  --transition-slow: all 0.5s ease;
}

/* ===== AMÉLIORATION DU BODY ET LAYOUT ===== */
body {
  font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  min-height: 100vh;
}

/* ===== AMÉLIORATION SIDEBAR EXISTANTE ===== */
#sidebar {
  background: linear-gradient(180deg, #1a202c 0%, #2d3748 100%) !important;
  backdrop-filter: blur(20px);
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.15) !important;
  border-right: 1px solid rgba(255, 255, 255, 0.1);
}

#sidebar .nav-link {
  border-radius: 12px !important;
  margin: 4px 12px !important;
  padding: 12px 16px !important;
  font-weight: 500 !important;
  transition: var(--transition-fast) !important;
}

#sidebar .nav-link:hover {
  background: rgba(102, 126, 234, 0.1) !important;
  transform: translateX(4px);
  color: var(--light-text) !important;
}

#sidebar .nav-link.active {
  background: var(--gradient-primary) !important;
  color: white !important;
  box-shadow: var(--shadow-md);
}

#sidebar .nav-link i {
  font-size: 1.1em;
  width: 20px;
  text-align: center;
}

/* ===== AMÉLIORATION CONTENU PRINCIPAL ===== */
#content {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(20px);
  border-radius: 20px 0 0 0 !important;
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1) !important;
}

/* ===== CARTES MODERNES ===== */
.card {
  border: none !important;
  border-radius: 16px !important;
  box-shadow: var(--shadow-md) !important;
  backdrop-filter: blur(10px);
  transition: var(--transition-normal) !important;
  overflow: hidden;
}

.card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg) !important;
}

.card-header {
  background: var(--gradient-primary) !important;
  color: white !important;
  border: none !important;
  padding: 1.5rem !important;
  font-weight: 600 !important;
}

.card-body {
  padding: 1.5rem !important;
}

/* ===== BOUTONS MODERNES ===== */
.btn {
  border-radius: 12px !important;
  padding: 12px 24px !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px;
  transition: var(--transition-fast) !important;
  border: none !important;
  position: relative;
  overflow: hidden;
}

.btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: var(--transition-normal);
}

.btn:hover::before {
  left: 100%;
}

.btn-primary {
  background: var(--gradient-primary) !important;
  box-shadow: var(--shadow-sm);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md) !important;
}

.btn-success {
  background: var(--gradient-success) !important;
}

.btn-warning {
  background: var(--gradient-warning) !important;
}

.btn-danger {
  background: var(--gradient-danger) !important;
}

.btn-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
}

.btn-info {
  background: linear-gradient(135deg, #17a2b8 0%, #138496 100%) !important;
}

/* ===== TABLEAUX MODERNES ===== */
.table {
  border-radius: 12px !important;
  overflow: hidden;
  box-shadow: var(--shadow-sm) !important;
}

.table thead th {
  background: var(--gradient-primary) !important;
  color: white !important;
  border: none !important;
  padding: 1rem !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px;
}

.table tbody tr {
  transition: var(--transition-fast) !important;
}

.table tbody tr:hover {
  background: rgba(102, 126, 234, 0.05) !important;
  transform: scale(1.01);
}

.table tbody td {
  padding: 1rem !important;
  border-color: rgba(0,0,0,0.05) !important;
  vertical-align: middle !important;
}

/* ===== BADGES MODERNES ===== */
.badge {
  border-radius: 20px !important;
  padding: 8px 16px !important;
  font-weight: 600 !important;
  letter-spacing: 0.5px;
  box-shadow: var(--shadow-sm);
}

.badge.bg-success {
  background: var(--gradient-success) !important;
}

.badge.bg-warning {
  background: var(--gradient-warning) !important;
}

.badge.bg-danger {
  background: var(--gradient-danger) !important;
}

.badge.bg-primary {
  background: var(--gradient-primary) !important;
}

/* ===== FORMULAIRES MODERNES ===== */
.form-control, .form-select {
  border-radius: 12px !important;
  border: 2px solid rgba(0,0,0,0.1) !important;
  padding: 12px 16px !important;
  transition: var(--transition-fast) !important;
  background: rgba(255,255,255,0.9) !important;
}

.form-control:focus, .form-select:focus {
  border-color: var(--primary-color) !important;
  box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
  background: white !important;
}

.form-label {
  font-weight: 600 !important;
  color: var(--dark-surface) !important;
  margin-bottom: 8px !important;
}

.input-group-text {
  background: rgba(102, 126, 234, 0.1) !important;
  border: 2px solid rgba(0,0,0,0.1) !important;
  border-right: none !important;
  border-radius: 12px 0 0 12px !important;
}

.input-group .form-control {
  border-left: none !important;
  border-radius: 0 12px 12px 0 !important;
}

/* ===== ALERTES MODERNES ===== */
.alert {
  border: none !important;
  border-radius: 12px !important;
  padding: 1rem 1.5rem !important;
  box-shadow: var(--shadow-sm) !important;
  backdrop-filter: blur(10px);
}

.alert-success {
  background: rgba(72, 187, 120, 0.1) !important;
  color: var(--success-color) !important;
  border-left: 4px solid var(--success-color) !important;
}

.alert-warning {
  background: rgba(237, 137, 54, 0.1) !important;
  color: var(--warning-color) !important;
  border-left: 4px solid var(--warning-color) !important;
}

.alert-danger {
  background: rgba(245, 101, 101, 0.1) !important;
  color: var(--danger-color) !important;
  border-left: 4px solid var(--danger-color) !important;
}

.alert-info {
  background: rgba(66, 153, 225, 0.1) !important;
  color: var(--info-color) !important;
  border-left: 4px solid var(--info-color) !important;
}

/* ===== ANIMATIONS ===== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.pulse-animation {
  animation: pulse 2s infinite;
}

/* ===== CONTENEURS MODERNES ===== */
.container {
  padding: 2rem !important;
}

/* ===== TITRES MODERNES ===== */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700 !important;
  color: var(--dark-surface) !important;
}

.display-1, .display-2, .display-3, .display-4, .display-5, .display-6 {
  font-weight: 800 !important;
}

/* ===== AMÉLIORATIONS DASHBOARD EXISTANT ===== */
.card.border-primary {
  border: none !important;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(102, 126, 234, 0.05) 100%) !important;
  border-left: 4px solid var(--primary-color) !important;
  transition: var(--transition-normal) !important;
}

.card.border-primary:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg) !important;
}

.card.border-success {
  border: none !important;
  background: linear-gradient(135deg, rgba(72, 187, 120, 0.1) 0%, rgba(72, 187, 120, 0.05) 100%) !important;
  border-left: 4px solid var(--success-color) !important;
  transition: var(--transition-normal) !important;
}

.card.border-success:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg) !important;
}

.card.border-danger {
  border: none !important;
  background: linear-gradient(135deg, rgba(245, 101, 101, 0.1) 0%, rgba(245, 101, 101, 0.05) 100%) !important;
  border-left: 4px solid var(--danger-color) !important;
  transition: var(--transition-normal) !important;
}

.card.border-danger:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg) !important;
}

.card.border-warning {
  border: none !important;
  background: linear-gradient(135deg, rgba(237, 137, 54, 0.1) 0%, rgba(237, 137, 54, 0.05) 100%) !important;
  border-left: 4px solid var(--warning-color) !important;
  transition: var(--transition-normal) !important;
}

.card.border-warning:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-lg) !important;
}

.card h2[data-stat] {
  font-size: 3rem !important;
  font-weight: 800 !important;
  margin: 0 !important;
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.card h6 {
  font-weight: 600 !important;
  text-transform: uppercase !important;
  letter-spacing: 1px !important;
  color: var(--muted-text) !important;
  margin-bottom: 1rem !important;
}

/* Amélioration des cartes de statistiques spécifiques */
.card.shadow-sm {
  transition: var(--transition-normal) !important;
}

.card.shadow-sm:hover {
  transform: translateY(-6px);
  box-shadow: var(--shadow-xl) !important;
}

/* Amélioration des icônes dans les cartes */
.card .bi {
  transition: var(--transition-fast);
}

.card:hover .bi {
  transform: scale(1.1);
}

/* ===== RESPONSIVE ===== */
@media (max-width: 768px) {
  .container {
    padding: 1rem !important;
  }

  .card-body {
    padding: 1rem !important;
  }

  .btn {
    padding: 10px 20px !important;
  }

  .display-4 {
    font-size: 2rem !important;
  }

  .btn-lg {
    padding: 8px 16px;
    font-size: 1rem;
  }
}
